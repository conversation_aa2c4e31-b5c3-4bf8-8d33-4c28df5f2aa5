# Souls Packager
An electron packaging repo to build a souls native application using the source code of flowise with minimal modifications.

## What makes this repo special?
The subfolder for electron-flowise and electron-souls have the packaging setup for the gitmodules Flowise (originally from flowise). This means that you can run `git submodule update --init --recursive` to get the latest code from the source repos, and re-run any build steps.

## What makes this repo different from the source repos?
There are several learnings going into packaging for electron, and for posterity, see the below information:

1. pnpm and electron do not really work well together, therefore we are using npm native install and build, with the install script to slightly modify the gitmodules to have them install correctly.
2. the package.json in the electron-* from root have the build information for electron as well as importing the correct gitmodule's packages.
3. by separating the source repo from the packaging step, we can more deterministically modify the electron packaging process from the underlying modification of the source code. This will help have modifications and updates to the source code be more easily managed. Also, as illustrated in this repo, we are using Flowise 
as a gitmodule, when ready we can replace this with a fork that we also manage.

## How to run:
```bash
// install and build the electron app from scratch (also usable in general)
./install.sh

// run the electron app with ollama server
./install.sh --ollama


```

## How to build/package:
Choose one of the electron-* folders (say electron-flowise), and then run the packaging command. Please note the packaging step will take a long time (estimated nearly 2 hours).  

```bash
cd ./electron-flowise
npm run package:mac
```

# Electron lifecycle and context information

1. From the app.asar, the built main file will run the main electron process.
2. the main file will spawn (technically fork) a sub process that will run electron.
3. the sub process will run the server code from the gitmodule (Flowise) and wait for it to be available.
4. the main process will load the renderer (webpage) from the build of the gitmodule (Flowise) and wait for it to be available.
5. once both the server, the main process will load the renderer into the main window.
6. all logs can be found at ~/Library/Logs/souls-flowise-monorepo

## Souls App file breakdown
The Souls app has some nuance that is important to note to ensure the app functions. The app itself has a very minimal electron app build process (simply a main, preload, and renderer build and archive process). 

Inside the Souls.app: app.asar (read only archive), under Resources, contains the following:
1. the build of the vite-electron (main, preload, renderer) that will run when running electron.
2. the node_modules of the entire application (hoisted dependencies as much as possible)

Inside the Souls.app: app sub folder (read/write execution code), again under Resources, contains the following:
1. The gitmodule build (Flowise) and its necessary code to run (just including the compiled js code and necessary node_modules, etc. to run the code).
2. a copy of all the node_modules in the app.asar (at least until we can separate further the electron dependencies from the forked-server dependencies).


# What if I want to tweak without waiting 2 hours each time?
After the first build, you can modify the SOULs dist app by exposing the underling package contents. There you will find the break up of the souls app.

Modifying the app sub folder in resources will allow you to modify the server code and restart the server to see changes. Ensure the application is closed, and then modify the app sub folder. 

Modifying the app.asar will require you to rebuild the app.asar and replace the original. You can still extract and then repack the app.asar, by running the following command:

```bash
// cd in the resources folder (from repo root)
cd electron-flowise/dist/mac-arm64/SOULS.app/Contents/Resources

// extract the app archive into a new folder (extracted/)
npx @electron/asar extract app.asar extracted

// make the modifications in the extracted folder (i.e. open code editor in extracted folder)
code extracted/

// in some cases you will modify the code in electron-flowise, and then run the simple electron build command. This only applies with the source code for electron (main, preload, renderer).
cd ../../../../electron-flowise
npm run build:electron

// repack the app archive back from the Resources folder
cd electron-flowise/dist/mac-arm64/SOULS.app/Contents/Resources
npx @electron/asar pack extracted app.asar
```

By modifying the underlying source code of the app, you 



# Known issues
1. The build process is very slow. This is due to the large number of dependencies and the fact that we are building from source. This is a work in progress, and we are looking to improve the build process.

2. When running the application, the error outputs 3 errors. (two for community langchain errors, and one for couchbase). These are not fatal, but may have issues connecting with perplexity chat via langchain and couchbase.

```log
[ERROR]: ❌ [server]: Error during initDatabase with file /Users/<USER>/Repos/souls-packager/electron-flowise/dist/mac-arm64/SOULS.app/Contents/Resources/app/node_modules/flowise-components/dist/nodes/chatmodels/ChatPerplexity/ChatPerplexity.js: Package subpath './chat_models/perplexity' is not defined by "exports" in /Users/<USER>/Repos/souls-packager/electron-flowise/dist/mac-arm64/SOULS.app/Contents/Resources/app/node_modules/@langchain/community/package.json

[ERROR]: ❌ [server]: Error during initDatabase with file /Users/<USER>/Repos/souls-packager/electron-flowise/dist/mac-arm64/SOULS.app/Contents/Resources/app/node_modules/flowise-components/dist/nodes/chatmodels/ChatPerplexity/FlowiseChatPerplexity.js: Package subpath './chat_models/perplexity' is not defined by "exports" in /Users/<USER>/Repos/souls-packager/electron-flowise/dist/mac-arm64/SOULS.app/Contents/Resources/app/node_modules/@langchain/community/package.json

[ERROR]: ❌ [server]: Error during initDatabase with file /Users/<USER>/Repos/souls-packager/electron-flowise/dist/mac-arm64/SOULS.app/Contents/Resources/app/node_modules/flowise-components/dist/nodes/vectorstores/Couchbase/Couchbase.js: Could not find native build for platform=darwin, arch=arm64, runtime=electron, nodeVersion=22.17.0, sslType=boringssl loaded from /Users/<USER>/Repos/souls-packager/electron-flowise/dist/mac-arm64/SOULS.app/Contents/Resources/app/node_modules/couchbase.
```