// paper & background
$paper: #ffffff;

// primary - Orange theme
$primaryLight: #fef6ea;
$primaryMain: #f7b359;
$primaryDark: #cc9449;
$primary200: #fde8cd;
$primary800: #75552a;

// primary color variants
$primary50: #fef6ea;
$primary100: #fde8cd;
$primary300: #fbdbb0;
$primary400: #face93;
$primary500: #f8c076;
$primary600: #cc9449;
$primary700: #a1743a;
$primary900: #4a361b;

// secondary - Cyan theme
$secondaryLight: #e1f7fa;
$secondaryMain: #0cc1da;
$secondaryDark: #0a9fb4;
$secondary200: #b6ecf4;
$secondary800: #065c68;

// secondary color variants
$secondary50: #e1f7fa;
$secondary100: #b6ecf4;
$secondary300: #8ce2ed;
$secondary400: #61d7e7;
$secondary500: #37cce0;
$secondary600: #0a9fb4;
$secondary700: #087d8e;
$secondary900: #043a41;

// success Colors - Green theme
$successLight: #e7fcef;
$success200: #c6f7d8;
$successMain: #42e37d;
$successDark: #36bb67;

// success color variants
$success50: #e7fcef;
$success100: #c6f7d8;
$success300: #a5f2c1;
$success400: #84edab;
$success500: #63e894;
$success600: #36bb67;
$success700: #2b9451;
$success800: #1f6c3b;
$success900: #144426;

// error - Red theme
$errorLight: #fce8e7;
$errorMain: #e34742;
$errorDark: #bb3b36;

// error color variants
$error50: #fce8e7;
$error100: #f7c8c6;
$error200: #f2a8a5;
$error300: #ed8784;
$error400: #e86763;
$error500: #e34742;
$error600: #bb3b36;
$error700: #942e2b;
$error800: #6c221f;
$error900: #441514;

// orange - Yellow theme
$orangeLight: #fff9e7;
$orangeMain: #ffcf41;
$orangeDark: #d2ab36;

// orange color variants
$orange50: #fff9e7;
$orange100: #fff1c6;
$orange200: #ffe8a5;
$orange300: #ffe084;
$orange400: #ffd762;
$orange500: #ffcf41;
$orange600: #d2ab36;
$orange700: #a6872a;
$orange800: #79621f;
$orange900: #4d3e14;

// brown - Teal theme
$tealLight: #e1f7fa;
$tealMain: #0cc1da;
$tealDark: #0a9fb4;

// teal color variants
$teal50: #e1f7fa;
$teal100: #b6ecf4;
$teal200: #8ce2ed;
$teal300: #61d7e7;
$teal400: #37cce0;
$teal500: #0cc1da;
$teal600: #0a9fb4;
$teal700: #087d8e;
$teal800: #065c68;
$teal900: #043a41;

// warning - Yellow theme
$warningLight: #fff9e7;
$warningMain: #ffcf41;
$warningDark: #d2ab36;

// warning color variants
$warning50: #fff9e7;
$warning100: #fff1c6;
$warning200: #ffe8a5;
$warning300: #ffe084;
$warning400: #ffd762;
$warning500: #ffcf41;
$warning600: #d2ab36;
$warning700: #a6872a;
$warning800: #79621f;
$warning900: #4d3e14;

// grey - Default theme
$grey50: #ededed;
$grey100: #d4d4d4;
$grey200: #bbbbbb;
$grey300: #a2a2a2;
$grey400: #898989;
$grey500: #707070;
$grey600: #5c5c5c;
$grey700: #494949;
$grey800: #353535;
$grey900: #222222;

// transparent
$transparent: #ffffff00;

// ==============================|| DARK THEME VARIANTS ||============================== //

// paper & background
$darkBackground: #0E0F12;
$darkPaper: #0E0F12;

// dark 800 & 900
$darkLevel1: #18181b; // level 1
$darkLevel2: #27272a; // level 2

// primary dark - Orange theme
$darkPrimaryLight: #4a361b;
$darkPrimaryMain: #f7b359;
$darkPrimaryDark: #75552a;
$darkPrimary200: #a1743a;
$darkPrimary800: #cc9449;

// primary dark color variants
$darkPrimary50: #4a361b;
$darkPrimary100: #75552a;
$darkPrimary300: #a1743a;
$darkPrimary400: #cc9449;
$darkPrimary500: #f7b359;
$darkPrimary600: #f8c076;
$darkPrimary700: #face93;
$darkPrimary900: #fef6ea;

// secondary dark - Cyan theme
$darkSecondaryLight: #043a41;
$darkSecondaryMain: #0cc1da;
$darkSecondaryDark: #065c68;
$darkSecondary200: #087d8e;
$darkSecondary800: #0a9fb4;

// secondary dark color variants
$darkSecondary50: #043a41;
$darkSecondary100: #065c68;
$darkSecondary300: #087d8e;
$darkSecondary400: #0a9fb4;
$darkSecondary500: #0cc1da;
$darkSecondary600: #37cce0;
$darkSecondary700: #61d7e7;
$darkSecondary800: #8ce2ed;
$darkSecondary900: #e1f7fa;

// text variants
$darkTextTitle: #F8F8F8;
$darkTextPrimary: #F8F8F8;
$darkTextSecondary: #868686;

// ==============================|| JAVASCRIPT ||============================== //

:export {
    // paper & background
    paper: $paper;

    // primary
    primaryLight: $primaryLight;
    primary200: $primary200;
    primaryMain: $primaryMain;
    primaryDark: $primaryDark;
    primary800: $primary800;
    primary50: $primary50;
    primary100: $primary100;
    primary300: $primary300;
    primary400: $primary400;
    primary500: $primary500;
    primary600: $primary600;
    primary700: $primary700;
    primary900: $primary900;

    // secondary
    secondaryLight: $secondaryLight;
    secondary200: $secondary200;
    secondaryMain: $secondaryMain;
    secondaryDark: $secondaryDark;
    secondary800: $secondary800;
    secondary50: $secondary50;
    secondary100: $secondary100;
    secondary300: $secondary300;
    secondary400: $secondary400;
    secondary500: $secondary500;
    secondary600: $secondary600;
    secondary700: $secondary700;
    secondary900: $secondary900;

    // success
    successLight: $successLight;
    success200: $success200;
    successMain: $successMain;
    successDark: $successDark;
    success50: $success50;
    success100: $success100;
    success300: $success300;
    success400: $success400;
    success500: $success500;
    success600: $success600;
    success700: $success700;
    success800: $success800;
    success900: $success900;

    // error
    errorLight: $errorLight;
    errorMain: $errorMain;
    errorDark: $errorDark;
    error50: $error50;
    error100: $error100;
    error200: $error200;
    error300: $error300;
    error400: $error400;
    error500: $error500;
    error600: $error600;
    error700: $error700;
    error800: $error800;
    error900: $error900;

    // orange
    orangeLight: $orangeLight;
    orangeMain: $orangeMain;
    orangeDark: $orangeDark;
    orange50: $orange50;
    orange100: $orange100;
    orange200: $orange200;
    orange300: $orange300;
    orange400: $orange400;
    orange500: $orange500;
    orange600: $orange600;
    orange700: $orange700;
    orange800: $orange800;
    orange900: $orange900;

    // teal
    tealLight: $tealLight;
    tealMain: $tealMain;
    tealDark: $tealDark;
    teal50: $teal50;
    teal100: $teal100;
    teal200: $teal200;
    teal300: $teal300;
    teal400: $teal400;
    teal500: $teal500;
    teal600: $teal600;
    teal700: $teal700;
    teal800: $teal800;
    teal900: $teal900;

    // warning
    warningLight: $warningLight;
    warningMain: $warningMain;
    warningDark: $warningDark;
    warning50: $warning50;
    warning100: $warning100;
    warning200: $warning200;
    warning300: $warning300;
    warning400: $warning400;
    warning500: $warning500;
    warning600: $warning600;
    warning700: $warning700;
    warning800: $warning800;
    warning900: $warning900;

    // grey
    grey50: $grey50;
    grey100: $grey100;
    grey200: $grey200;
    grey300: $grey300;
    grey400: $grey400;
    grey500: $grey500;
    grey600: $grey600;
    grey700: $grey700;
    grey800: $grey800;
    grey900: $grey900;

    // ==============================|| DARK THEME VARIANTS ||============================== //

    // paper & background
    darkPaper: $darkPaper;
    darkBackground: $darkBackground;

    // dark 800 & 900
    darkLevel1: $darkLevel1;
    darkLevel2: $darkLevel2;

    // text variants
    darkTextTitle: $darkTextTitle;
    darkTextPrimary: $darkTextPrimary;
    darkTextSecondary: $darkTextSecondary;

    // primary dark
    darkPrimaryLight: $darkPrimaryLight;
    darkPrimaryMain: $darkPrimaryMain;
    darkPrimaryDark: $darkPrimaryDark;
    darkPrimary200: $darkPrimary200;
    darkPrimary800: $darkPrimary800;
    darkPrimary50: $darkPrimary50;
    darkPrimary100: $darkPrimary100;
    darkPrimary300: $darkPrimary300;
    darkPrimary400: $darkPrimary400;
    darkPrimary500: $darkPrimary500;
    darkPrimary600: $darkPrimary600;
    darkPrimary700: $darkPrimary700;
    darkPrimary900: $darkPrimary900;

    // secondary dark
    darkSecondaryLight: $darkSecondaryLight;
    darkSecondaryMain: $darkSecondaryMain;
    darkSecondaryDark: $darkSecondaryDark;
    darkSecondary200: $darkSecondary200;
    darkSecondary800: $darkSecondary800;
    darkSecondary50: $darkSecondary50;
    darkSecondary100: $darkSecondary100;
    darkSecondary300: $darkSecondary300;
    darkSecondary400: $darkSecondary400;
    darkSecondary500: $darkSecondary500;
    darkSecondary600: $darkSecondary600;
    darkSecondary700: $darkSecondary700;
    darkSecondary800: $darkSecondary800;
    darkSecondary900: $darkSecondary900;

    // transparent
    transparent: $transparent;
}