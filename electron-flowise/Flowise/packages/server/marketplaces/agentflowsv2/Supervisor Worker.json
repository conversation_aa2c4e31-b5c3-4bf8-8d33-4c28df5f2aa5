{"description": "A hierarchical supervisor agent that plan the steps, and delegate tasks to worker agents based on user query", "usecases": ["Hierarchical Agent Teams"], "nodes": [{"id": "startAgentflow_0", "type": "agent<PERSON>low", "position": {"x": -198.4357561998925, "y": 90.62378754136287}, "data": {"id": "startAgentflow_0", "label": "Start", "version": 1.1, "name": "startAgentflow", "type": "Start", "color": "#7EE787", "hideInput": true, "baseClasses": ["Start"], "category": "Agent Flows", "description": "Starting point of the agentflow", "inputParams": [{"label": "Input Type", "name": "startInputType", "type": "options", "options": [{"label": "Chat Input", "name": "chatInput", "description": "Start the conversation with chat input"}, {"label": "Form Input", "name": "formInput", "description": "Start the workflow with form inputs"}], "default": "chatInput", "id": "startAgentflow_0-input-startInputType-options", "display": true}, {"label": "Form Title", "name": "formTitle", "type": "string", "placeholder": "Please Fill Out The Form", "show": {"startInputType": "formInput"}, "id": "startAgentflow_0-input-formTitle-string", "display": false}, {"label": "Form Description", "name": "formDescription", "type": "string", "placeholder": "Complete all fields below to continue", "show": {"startInputType": "formInput"}, "id": "startAgentflow_0-input-formDescription-string", "display": false}, {"label": "Form Input Types", "name": "formInputTypes", "description": "Specify the type of form input", "type": "array", "show": {"startInputType": "formInput"}, "array": [{"label": "Type", "name": "type", "type": "options", "options": [{"label": "String", "name": "string"}, {"label": "Number", "name": "number"}, {"label": "Boolean", "name": "boolean"}, {"label": "Options", "name": "options"}], "default": "string"}, {"label": "Label", "name": "label", "type": "string", "placeholder": "Label for the input"}, {"label": "Variable Name", "name": "name", "type": "string", "placeholder": "Variable name for the input (must be camel case)", "description": "Variable name must be camel case. For example: firstName, lastName, etc."}, {"label": "Add Options", "name": "addOptions", "type": "array", "show": {"formInputTypes[$index].type": "options"}, "array": [{"label": "Option", "name": "option", "type": "string"}]}], "id": "startAgentflow_0-input-formInputTypes-array", "display": false}, {"label": "Ephemeral Memory", "name": "startEphemeralMemory", "type": "boolean", "description": "Start fresh for every execution without past chat history", "optional": true}, {"label": "Flow State", "name": "startState", "description": "Runtime state during the execution of the workflow", "type": "array", "optional": true, "array": [{"label": "Key", "name": "key", "type": "string", "placeholder": "Foo"}, {"label": "Value", "name": "value", "type": "string", "placeholder": "Bar"}], "id": "startAgentflow_0-input-startState-array", "display": true}, {"label": "Persist State", "name": "startPersistState", "type": "boolean", "description": "Persist the state in the same session", "optional": true, "id": "startAgentflow_0-input-startPersistState-boolean", "display": true}], "inputAnchors": [], "inputs": {"startInputType": "chatInput", "formTitle": "", "formDescription": "", "formInputTypes": "", "startState": [{"key": "next", "value": ""}, {"key": "instruction", "value": ""}]}, "outputAnchors": [{"id": "startAgentflow_0-output-startAgentflow", "label": "Start", "name": "startAgentflow"}], "outputs": {}, "selected": false}, "width": 103, "height": 66, "selected": false, "positionAbsolute": {"x": -198.4357561998925, "y": 90.62378754136287}, "dragging": false}, {"id": "conditionAgentflow_0", "position": {"x": 128.47781848153903, "y": 73.36847122134466}, "data": {"id": "conditionAgentflow_0", "label": "Check next worker", "version": 1, "name": "conditionAgentflow", "type": "Condition", "color": "#FFB938", "baseClasses": ["Condition"], "category": "Agent Flows", "description": "Split flows based on If Else conditions", "inputParams": [{"label": "Conditions", "name": "conditions", "type": "array", "description": "Values to compare", "acceptVariable": true, "default": [{"type": "string", "value1": "<p><span class=\"variable\" data-type=\"mention\" data-id=\"$flow.state.next\" data-label=\"$flow.state.next\">{{ $flow.state.next }}</span> </p>", "operation": "equal", "value2": "<p>SOFTWARE</p>"}], "array": [{"label": "Type", "name": "type", "type": "options", "options": [{"label": "String", "name": "string"}, {"label": "Number", "name": "number"}, {"label": "Boolean", "name": "boolean"}], "default": "string"}, {"label": "Value 1", "name": "value1", "type": "string", "default": "", "description": "First value to be compared with", "acceptVariable": true, "show": {"conditions[$index].type": "string"}}, {"label": "Operation", "name": "operation", "type": "options", "options": [{"label": "Contains", "name": "contains"}, {"label": "Ends With", "name": "endsWith"}, {"label": "Equal", "name": "equal"}, {"label": "Not Contains", "name": "notContains"}, {"label": "Not Equal", "name": "notEqual"}, {"label": "Regex", "name": "regex"}, {"label": "Starts With", "name": "startsWith"}, {"label": "Is Empty", "name": "isEmpty"}, {"label": "Not Empty", "name": "notEmpty"}], "default": "equal", "description": "Type of operation", "show": {"conditions[$index].type": "string"}}, {"label": "Value 2", "name": "value2", "type": "string", "default": "", "description": "Second value to be compared with", "acceptVariable": true, "show": {"conditions[$index].type": "string"}, "hide": {"conditions[$index].operation": ["isEmpty", "notEmpty"]}}, {"label": "Value 1", "name": "value1", "type": "number", "default": "", "description": "First value to be compared with", "acceptVariable": true, "show": {"conditions[$index].type": "number"}}, {"label": "Operation", "name": "operation", "type": "options", "options": [{"label": "Smaller", "name": "smaller"}, {"label": "Smaller Equal", "name": "smallerEqual"}, {"label": "Equal", "name": "equal"}, {"label": "Not Equal", "name": "notEqual"}, {"label": "Larger", "name": "larger"}, {"label": "Larger Equal", "name": "largerEqual"}, {"label": "Is Empty", "name": "isEmpty"}, {"label": "Not Empty", "name": "notEmpty"}], "default": "equal", "description": "Type of operation", "show": {"conditions[$index].type": "number"}}, {"label": "Value 2", "name": "value2", "type": "number", "default": 0, "description": "Second value to be compared with", "acceptVariable": true, "show": {"conditions[$index].type": "number"}}, {"label": "Value 1", "name": "value1", "type": "boolean", "default": false, "description": "First value to be compared with", "show": {"conditions[$index].type": "boolean"}}, {"label": "Operation", "name": "operation", "type": "options", "options": [{"label": "Equal", "name": "equal"}, {"label": "Not Equal", "name": "notEqual"}], "default": "equal", "description": "Type of operation", "show": {"conditions[$index].type": "boolean"}}, {"label": "Value 2", "name": "value2", "type": "boolean", "default": false, "description": "Second value to be compared with", "show": {"conditions[$index].type": "boolean"}}], "id": "conditionAgentflow_0-input-conditions-array", "display": true}], "inputAnchors": [], "inputs": {"conditions": [{"type": "string", "value1": "<p><span class=\"variable\" data-type=\"mention\" data-id=\"$flow.state.next\" data-label=\"$flow.state.next\">{{ $flow.state.next }}</span> </p>", "operation": "equal", "value2": "<p>SOFTWARE</p>"}, {"type": "string", "value1": "<p><span class=\"variable\" data-type=\"mention\" data-id=\"$flow.state.next\" data-label=\"$flow.state.next\">{{ $flow.state.next }}</span> </p>", "operation": "equal", "value2": "<p>REVIEWER</p>"}]}, "outputAnchors": [{"id": "conditionAgentflow_0-output-0", "label": 0, "name": 0, "description": "Condition 0"}, {"id": "conditionAgentflow_0-output-1", "label": 1, "name": 1, "description": "Condition 1"}, {"id": "conditionAgentflow_0-output-2", "label": 2, "name": 2, "description": "Else"}], "outputs": {"conditionAgentflow": ""}, "selected": false}, "type": "agent<PERSON>low", "width": 194, "height": 100, "selected": false, "positionAbsolute": {"x": 128.47781848153903, "y": 73.36847122134466}, "dragging": false}, {"id": "agentAgentflow_1", "position": {"x": 352.5679347768288, "y": -23.510778245391947}, "data": {"id": "agentAgentflow_1", "label": "Software Engineer", "version": 1, "name": "agentAgentflow", "type": "Agent", "color": "#4DD0E1", "baseClasses": ["Agent"], "category": "Agent Flows", "description": "Dynamically choose and utilize tools during runtime, enabling multi-step reasoning", "inputParams": [{"label": "Model", "name": "agentModel", "type": "asyncOptions", "loadMethod": "listModels", "loadConfig": true, "id": "agentAgentflow_1-input-agentModel-asyncOptions", "display": true}, {"label": "Messages", "name": "agentMessages", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Role", "name": "role", "type": "options", "options": [{"label": "System", "name": "system"}, {"label": "Assistant", "name": "assistant"}, {"label": "Developer", "name": "developer"}, {"label": "User", "name": "user"}]}, {"label": "Content", "name": "content", "type": "string", "acceptVariable": true, "generateInstruction": true, "rows": 4}], "id": "agentAgentflow_1-input-agentMessages-array", "display": true}, {"label": "Tools", "name": "agentTools", "type": "array", "optional": true, "array": [{"label": "Tool", "name": "agentSelectedTool", "type": "asyncOptions", "loadMethod": "listTools", "loadConfig": true}, {"label": "Require Human Input", "name": "agentSelectedToolRequiresHumanInput", "type": "boolean", "optional": true}], "id": "agentAgentflow_1-input-agentTools-array", "display": true}, {"label": "Knowledge (Document Stores)", "name": "agentKnowledgeDocumentStores", "type": "array", "description": "Give your agent context about different document sources. Document stores must be upserted in advance.", "array": [{"label": "Document Store", "name": "documentStore", "type": "asyncOptions", "loadMethod": "listStores"}, {"label": "Describe Knowledge", "name": "docStoreDescription", "type": "string", "generateDocStoreDescription": true, "placeholder": "Describe what the knowledge base is about, this is useful for the AI to know when and how to search for correct information", "rows": 4}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true}], "optional": true, "id": "agentAgentflow_1-input-agentKnowledgeDocumentStores-array", "display": true}, {"label": "Knowledge (Vector Embeddings)", "name": "agentKnowledgeVSEmbeddings", "type": "array", "description": "Give your agent context about different document sources from existing vector stores and embeddings", "array": [{"label": "Vector Store", "name": "vectorStore", "type": "asyncOptions", "loadMethod": "listVectorStores", "loadConfig": true}, {"label": "Embedding Model", "name": "embeddingModel", "type": "asyncOptions", "loadMethod": "listEmbeddings", "loadConfig": true}, {"label": "Knowledge Name", "name": "knowledgeName", "type": "string", "placeholder": "A short name for the knowledge base, this is useful for the AI to know when and how to search for correct information"}, {"label": "Describe Knowledge", "name": "knowledgeDescription", "type": "string", "placeholder": "Describe what the knowledge base is about, this is useful for the AI to know when and how to search for correct information", "rows": 4}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true}], "optional": true, "id": "agentAgentflow_0-input-agentKnowledgeVSEmbeddings-array", "display": true}, {"label": "Enable Memory", "name": "agentEnableMemory", "type": "boolean", "description": "Enable memory for the conversation thread", "default": true, "optional": true, "id": "agentAgentflow_1-input-agentEnableMemory-boolean", "display": true}, {"label": "Memory Type", "name": "agentMemoryType", "type": "options", "options": [{"label": "All Messages", "name": "allMessages", "description": "Retrieve all messages from the conversation"}, {"label": "Window Size", "name": "windowSize", "description": "Uses a fixed window size to surface the last N messages"}, {"label": "Conversation Summary", "name": "conversation<PERSON><PERSON><PERSON><PERSON>", "description": "Summarizes the whole conversation"}, {"label": "Conversation Summary B<PERSON>er", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Summarize conversations once token limit is reached. Default to 2000"}], "optional": true, "default": "allMessages", "show": {"agentEnableMemory": true}, "id": "agentAgentflow_1-input-agentMemoryType-options", "display": true}, {"label": "Window Size", "name": "agentMemoryWindowSize", "type": "number", "default": "20", "description": "Uses a fixed window size to surface the last N messages", "show": {"agentMemoryType": "windowSize"}, "id": "agentAgentflow_1-input-agentMemoryWindowSize-number", "display": false}, {"label": "<PERSON>", "name": "agentMemoryMaxTokenLimit", "type": "number", "default": "2000", "description": "Summarize conversations once token limit is reached. Default to 2000", "show": {"agentMemoryType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "id": "agentAgentflow_1-input-agentMemoryMaxTokenLimit-number", "display": false}, {"label": "Input Message", "name": "agentUserMessage", "type": "string", "description": "Add an input message as user message at the end of the conversation", "rows": 4, "optional": true, "acceptVariable": true, "show": {"agentEnableMemory": true}, "id": "agentAgentflow_1-input-agentUserMessage-string", "display": true}, {"label": "Return Response As", "name": "agentReturnResponseAs", "type": "options", "options": [{"label": "User Message", "name": "userMessage"}, {"label": "Assistant Message", "name": "assistant<PERSON><PERSON><PERSON>"}], "default": "userMessage", "id": "agentAgentflow_1-input-agentReturnResponseAs-options", "display": true}, {"label": "Update Flow State", "name": "agentUpdateState", "description": "Update runtime state during the execution of the workflow", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Key", "name": "key", "type": "asyncOptions", "loadMethod": "listRuntimeStateKeys", "freeSolo": true}, {"label": "Value", "name": "value", "type": "string", "acceptVariable": true, "acceptNodeOutputAsVariable": true}], "id": "agentAgentflow_1-input-agentUpdateState-array", "display": true}], "inputAnchors": [], "inputs": {"agentModel": "chatOpenAI", "agentMessages": [{"role": "system", "content": "<p>As a Senior Software Engineer, you are a pivotal part of our innovative development team. Your expertise and leadership drive the creation of robust, scalable software solutions that meet the needs of our diverse clientele. By applying best practices in software development, you ensure that our products are reliable, efficient, and maintainable.</p><p>Your goal is to lead the development of high-quality software solutions.</p><p>Utilize your deep technical knowledge and experience to architect, design, and implement software systems that address complex problems. Collaborate closely with other engineers, reviewers to ensure that the solutions you develop align with business objectives and user needs.</p><p>Design and implement new feature for the given task, ensuring it integrates seamlessly with existing systems and meets performance requirements. Use your understanding of React, Tailwindcss, NodeJS to build this feature. Make sure to adhere to our coding standards and follow best practices.</p><p>The output should be a fully functional, well-documented feature that enhances our product's capabilities. Include detailed comments in the code.</p>"}], "agentTools": "", "agentKnowledgeDocumentStores": "", "agentEnableMemory": true, "agentMemoryType": "allMessages", "agentUserMessage": "<p><span class=\"variable\" data-type=\"mention\" data-id=\"$flow.state.instruction\" data-label=\"$flow.state.instruction\">{{ $flow.state.instruction }}</span></p>", "agentReturnResponseAs": "userMessage", "agentUpdateState": "", "agentModelConfig": {"credential": "", "modelName": "gpt-4o-mini", "temperature": 0.9, "streaming": true, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "strictToolCalling": "", "stopSequence": "", "basepath": "", "proxyUrl": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low", "reasoningEffort": "medium", "agentModel": "chatOpenAI"}}, "outputAnchors": [{"id": "agentAgentflow_1-output-agentAgentflow", "label": "Agent", "name": "agentAgentflow"}], "outputs": {}, "selected": false}, "type": "agent<PERSON>low", "width": 191, "height": 72, "selected": false, "positionAbsolute": {"x": 352.5679347768288, "y": -23.510778245391947}, "dragging": false}, {"id": "agentAgentflow_2", "position": {"x": 359.32908043399146, "y": 88.11650145737843}, "data": {"id": "agentAgentflow_2", "label": "Code Reviewer", "version": 1, "name": "agentAgentflow", "type": "Agent", "color": "#4DD0E1", "baseClasses": ["Agent"], "category": "Agent Flows", "description": "Dynamically choose and utilize tools during runtime, enabling multi-step reasoning", "inputParams": [{"label": "Model", "name": "agentModel", "type": "asyncOptions", "loadMethod": "listModels", "loadConfig": true, "id": "agentAgentflow_2-input-agentModel-asyncOptions", "display": true}, {"label": "Messages", "name": "agentMessages", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Role", "name": "role", "type": "options", "options": [{"label": "System", "name": "system"}, {"label": "Assistant", "name": "assistant"}, {"label": "Developer", "name": "developer"}, {"label": "User", "name": "user"}]}, {"label": "Content", "name": "content", "type": "string", "acceptVariable": true, "generateInstruction": true, "rows": 4}], "id": "agentAgentflow_2-input-agentMessages-array", "display": true}, {"label": "Tools", "name": "agentTools", "type": "array", "optional": true, "array": [{"label": "Tool", "name": "agentSelectedTool", "type": "asyncOptions", "loadMethod": "listTools", "loadConfig": true}, {"label": "Require Human Input", "name": "agentSelectedToolRequiresHumanInput", "type": "boolean", "optional": true}], "id": "agentAgentflow_2-input-agentTools-array", "display": true}, {"label": "Knowledge (Document Stores)", "name": "agentKnowledgeDocumentStores", "type": "array", "description": "Give your agent context about different document sources. Document stores must be upserted in advance.", "array": [{"label": "Document Store", "name": "documentStore", "type": "asyncOptions", "loadMethod": "listStores"}, {"label": "Describe Knowledge", "name": "docStoreDescription", "type": "string", "generateDocStoreDescription": true, "placeholder": "Describe what the knowledge base is about, this is useful for the AI to know when and how to search for correct information", "rows": 4}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true}], "optional": true, "id": "agentAgentflow_2-input-agentKnowledgeDocumentStores-array", "display": true}, {"label": "Knowledge (Vector Embeddings)", "name": "agentKnowledgeVSEmbeddings", "type": "array", "description": "Give your agent context about different document sources from existing vector stores and embeddings", "array": [{"label": "Vector Store", "name": "vectorStore", "type": "asyncOptions", "loadMethod": "listVectorStores", "loadConfig": true}, {"label": "Embedding Model", "name": "embeddingModel", "type": "asyncOptions", "loadMethod": "listEmbeddings", "loadConfig": true}, {"label": "Knowledge Name", "name": "knowledgeName", "type": "string", "placeholder": "A short name for the knowledge base, this is useful for the AI to know when and how to search for correct information"}, {"label": "Describe Knowledge", "name": "knowledgeDescription", "type": "string", "placeholder": "Describe what the knowledge base is about, this is useful for the AI to know when and how to search for correct information", "rows": 4}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true}], "optional": true, "id": "agentAgentflow_2-input-agentKnowledgeVSEmbeddings-array", "display": true}, {"label": "Enable Memory", "name": "agentEnableMemory", "type": "boolean", "description": "Enable memory for the conversation thread", "default": true, "optional": true, "id": "agentAgentflow_2-input-agentEnableMemory-boolean", "display": true}, {"label": "Memory Type", "name": "agentMemoryType", "type": "options", "options": [{"label": "All Messages", "name": "allMessages", "description": "Retrieve all messages from the conversation"}, {"label": "Window Size", "name": "windowSize", "description": "Uses a fixed window size to surface the last N messages"}, {"label": "Conversation Summary", "name": "conversation<PERSON><PERSON><PERSON><PERSON>", "description": "Summarizes the whole conversation"}, {"label": "Conversation Summary B<PERSON>er", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Summarize conversations once token limit is reached. Default to 2000"}], "optional": true, "default": "allMessages", "show": {"agentEnableMemory": true}, "id": "agentAgentflow_2-input-agentMemoryType-options", "display": true}, {"label": "Window Size", "name": "agentMemoryWindowSize", "type": "number", "default": "20", "description": "Uses a fixed window size to surface the last N messages", "show": {"agentMemoryType": "windowSize"}, "id": "agentAgentflow_2-input-agentMemoryWindowSize-number", "display": false}, {"label": "<PERSON>", "name": "agentMemoryMaxTokenLimit", "type": "number", "default": "2000", "description": "Summarize conversations once token limit is reached. Default to 2000", "show": {"agentMemoryType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "id": "agentAgentflow_2-input-agentMemoryMaxTokenLimit-number", "display": false}, {"label": "Input Message", "name": "agentUserMessage", "type": "string", "description": "Add an input message as user message at the end of the conversation", "rows": 4, "optional": true, "acceptVariable": true, "show": {"agentEnableMemory": true}, "id": "agentAgentflow_2-input-agentUserMessage-string", "display": true}, {"label": "Return Response As", "name": "agentReturnResponseAs", "type": "options", "options": [{"label": "User Message", "name": "userMessage"}, {"label": "Assistant Message", "name": "assistant<PERSON><PERSON><PERSON>"}], "default": "userMessage", "id": "agentAgentflow_2-input-agentReturnResponseAs-options", "display": true}, {"label": "Update Flow State", "name": "agentUpdateState", "description": "Update runtime state during the execution of the workflow", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Key", "name": "key", "type": "asyncOptions", "loadMethod": "listRuntimeStateKeys", "freeSolo": true}, {"label": "Value", "name": "value", "type": "string", "acceptVariable": true, "acceptNodeOutputAsVariable": true}], "id": "agentAgentflow_2-input-agentUpdateState-array", "display": true}], "inputAnchors": [], "inputs": {"agentModel": "chatOpenAI", "agentMessages": [{"role": "system", "content": "<p>As a Quality Assurance Engineer, you are an integral part of our development team, ensuring that our software products are of the highest quality. Your meticulous attention to detail and expertise in testing methodologies are crucial in identifying defects and ensuring that our code meets the highest standards.</p><p>Your goal is to ensure the delivery of high-quality software through thorough code review and testing.</p><p>Review the codebase for the new feature designed and implemented by the Senior Software Engineer. Your expertise goes beyond mere code inspection; you are adept at ensuring that developments not only function as intended but also adhere to the team's coding standards, enhance maintainability, and seamlessly integrate with existing systems.</p><p>With a deep appreciation for collaborative development, you provide constructive feedback, guiding contributors towards best practices and fostering a culture of continuous improvement. Your meticulous approach to reviewing code, coupled with your ability to foresee potential issues and recommend proactive solutions, ensures the delivery of high-quality software that is robust, scalable, and aligned with the team's strategic goals.</p>"}], "agentTools": "", "agentKnowledgeDocumentStores": "", "agentEnableMemory": true, "agentMemoryType": "allMessages", "agentUserMessage": "<p><span class=\"variable\" data-type=\"mention\" data-id=\"$flow.state.instruction\" data-label=\"$flow.state.instruction\">{{ $flow.state.instruction }}</span></p>", "agentReturnResponseAs": "userMessage", "agentUpdateState": "", "agentModelConfig": {"credential": "", "modelName": "gpt-4o-mini", "temperature": 0.9, "streaming": true, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "strictToolCalling": "", "stopSequence": "", "basepath": "", "proxyUrl": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low", "reasoningEffort": "medium", "agentModel": "chatOpenAI"}}, "outputAnchors": [{"id": "agentAgentflow_2-output-agentAgentflow", "label": "Agent", "name": "agentAgentflow"}], "outputs": {}, "selected": false}, "type": "agent<PERSON>low", "width": 175, "height": 72, "selected": false, "positionAbsolute": {"x": 359.32908043399146, "y": 88.11650145737843}, "dragging": false}, {"id": "agentAgentflow_3", "position": {"x": 357.60470406099364, "y": 192.61532204982643}, "data": {"id": "agentAgentflow_3", "label": "Generate Final Answer", "version": 1, "name": "agentAgentflow", "type": "Agent", "color": "#4DD0E1", "baseClasses": ["Agent"], "category": "Agent Flows", "description": "Dynamically choose and utilize tools during runtime, enabling multi-step reasoning", "inputParams": [{"label": "Model", "name": "agentModel", "type": "asyncOptions", "loadMethod": "listModels", "loadConfig": true, "id": "agentAgentflow_3-input-agentModel-asyncOptions", "display": true}, {"label": "Messages", "name": "agentMessages", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Role", "name": "role", "type": "options", "options": [{"label": "System", "name": "system"}, {"label": "Assistant", "name": "assistant"}, {"label": "Developer", "name": "developer"}, {"label": "User", "name": "user"}]}, {"label": "Content", "name": "content", "type": "string", "acceptVariable": true, "generateInstruction": true, "rows": 4}], "id": "agentAgentflow_3-input-agentMessages-array", "display": true}, {"label": "Tools", "name": "agentTools", "type": "array", "optional": true, "array": [{"label": "Tool", "name": "agentSelectedTool", "type": "asyncOptions", "loadMethod": "listTools", "loadConfig": true}, {"label": "Require Human Input", "name": "agentSelectedToolRequiresHumanInput", "type": "boolean", "optional": true}], "id": "agentAgentflow_3-input-agentTools-array", "display": true}, {"label": "Knowledge (Document Stores)", "name": "agentKnowledgeDocumentStores", "type": "array", "description": "Give your agent context about different document sources. Document stores must be upserted in advance.", "array": [{"label": "Document Store", "name": "documentStore", "type": "asyncOptions", "loadMethod": "listStores"}, {"label": "Describe Knowledge", "name": "docStoreDescription", "type": "string", "generateDocStoreDescription": true, "placeholder": "Describe what the knowledge base is about, this is useful for the AI to know when and how to search for correct information", "rows": 4}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true}], "optional": true, "id": "agentAgentflow_3-input-agentKnowledgeDocumentStores-array", "display": true}, {"label": "Knowledge (Vector Embeddings)", "name": "agentKnowledgeVSEmbeddings", "type": "array", "description": "Give your agent context about different document sources from existing vector stores and embeddings", "array": [{"label": "Vector Store", "name": "vectorStore", "type": "asyncOptions", "loadMethod": "listVectorStores", "loadConfig": true}, {"label": "Embedding Model", "name": "embeddingModel", "type": "asyncOptions", "loadMethod": "listEmbeddings", "loadConfig": true}, {"label": "Knowledge Name", "name": "knowledgeName", "type": "string", "placeholder": "A short name for the knowledge base, this is useful for the AI to know when and how to search for correct information"}, {"label": "Describe Knowledge", "name": "knowledgeDescription", "type": "string", "placeholder": "Describe what the knowledge base is about, this is useful for the AI to know when and how to search for correct information", "rows": 4}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true}], "optional": true, "id": "agentAgentflow_3-input-agentKnowledgeVSEmbeddings-array", "display": true}, {"label": "Enable Memory", "name": "agentEnableMemory", "type": "boolean", "description": "Enable memory for the conversation thread", "default": true, "optional": true, "id": "agentAgentflow_3-input-agentEnableMemory-boolean", "display": true}, {"label": "Memory Type", "name": "agentMemoryType", "type": "options", "options": [{"label": "All Messages", "name": "allMessages", "description": "Retrieve all messages from the conversation"}, {"label": "Window Size", "name": "windowSize", "description": "Uses a fixed window size to surface the last N messages"}, {"label": "Conversation Summary", "name": "conversation<PERSON><PERSON><PERSON><PERSON>", "description": "Summarizes the whole conversation"}, {"label": "Conversation Summary B<PERSON>er", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Summarize conversations once token limit is reached. Default to 2000"}], "optional": true, "default": "allMessages", "show": {"agentEnableMemory": true}, "id": "agentAgentflow_3-input-agentMemoryType-options", "display": true}, {"label": "Window Size", "name": "agentMemoryWindowSize", "type": "number", "default": "20", "description": "Uses a fixed window size to surface the last N messages", "show": {"agentMemoryType": "windowSize"}, "id": "agentAgentflow_3-input-agentMemoryWindowSize-number", "display": false}, {"label": "<PERSON>", "name": "agentMemoryMaxTokenLimit", "type": "number", "default": "2000", "description": "Summarize conversations once token limit is reached. Default to 2000", "show": {"agentMemoryType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "id": "agentAgentflow_3-input-agentMemoryMaxTokenLimit-number", "display": false}, {"label": "Input Message", "name": "agentUserMessage", "type": "string", "description": "Add an input message as user message at the end of the conversation", "rows": 4, "optional": true, "acceptVariable": true, "show": {"agentEnableMemory": true}, "id": "agentAgentflow_3-input-agentUserMessage-string", "display": true}, {"label": "Return Response As", "name": "agentReturnResponseAs", "type": "options", "options": [{"label": "User Message", "name": "userMessage"}, {"label": "Assistant Message", "name": "assistant<PERSON><PERSON><PERSON>"}], "default": "userMessage", "id": "agentAgentflow_3-input-agentReturnResponseAs-options", "display": true}, {"label": "Update Flow State", "name": "agentUpdateState", "description": "Update runtime state during the execution of the workflow", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Key", "name": "key", "type": "asyncOptions", "loadMethod": "listRuntimeStateKeys", "freeSolo": true}, {"label": "Value", "name": "value", "type": "string", "acceptVariable": true, "acceptNodeOutputAsVariable": true}], "id": "agentAgentflow_3-input-agentUpdateState-array", "display": true}], "inputAnchors": [], "inputs": {"agentModel": "chatGoogleGenerativeAI", "agentMessages": "", "agentTools": "", "agentKnowledgeDocumentStores": "", "agentEnableMemory": true, "agentMemoryType": "allMessages", "agentUserMessage": "<p>Given the above conversations, generate a detail solution developed by the software engineer and code reviewer. </p><p>Your guiding principles:</p><ol><li><p><strong>Preserve Full Context</strong><br>Include all code implementations, improvements and review from the conversation. Do not omit, summarize, or oversimplify key information.</p></li><li><p><strong>Markdown Output Only</strong><br>Your final output must be in Markdown format.</p></li></ol>", "agentReturnResponseAs": "userMessage", "agentUpdateState": "", "agentModelConfig": {"credential": "", "modelName": "gemini-2.5-flash-preview-05-20", "customModelName": "", "temperature": 0.9, "streaming": true, "maxOutputTokens": "", "topP": "", "topK": "", "harmCategory": "", "harmBlockThreshold": "", "baseUrl": "", "allowImageUploads": "", "agentModel": "chatGoogleGenerativeAI"}}, "outputAnchors": [{"id": "agentAgentflow_3-output-agentAgentflow", "label": "Agent", "name": "agentAgentflow"}], "outputs": {}, "selected": false}, "type": "agent<PERSON>low", "width": 283, "height": 72, "selected": false, "positionAbsolute": {"x": 357.60470406099364, "y": 192.61532204982643}, "dragging": false}, {"id": "loopAgentflow_0", "position": {"x": 572.5888618465789, "y": -20.827003962303266}, "data": {"id": "loopAgentflow_0", "label": "Loop to Supervisor", "version": 1, "name": "loopAgentflow", "type": "Loop", "color": "#FFA07A", "hideOutput": true, "baseClasses": ["Loop"], "category": "Agent Flows", "description": "Loop back to a previous node", "inputParams": [{"label": "Loop Back To", "name": "loopBackToNode", "type": "asyncOptions", "loadMethod": "listPreviousNodes", "freeSolo": true, "id": "loopAgentflow_0-input-loopBackToNode-asyncOptions", "display": true}, {"label": "Max Loop Count", "name": "max<PERSON>oop<PERSON>ount", "type": "number", "default": 5, "id": "loopAgentflow_0-input-maxLoopCount-number", "display": true}], "inputAnchors": [], "inputs": {"loopBackToNode": "llmAgentflow_0-Supervisor", "maxLoopCount": 5}, "outputAnchors": [], "outputs": {}, "selected": false}, "type": "agent<PERSON>low", "width": 195, "height": 66, "selected": false, "dragging": false, "positionAbsolute": {"x": 572.5888618465789, "y": -20.827003962303266}}, {"id": "loopAgentflow_1", "position": {"x": 566.7568359277939, "y": 90.98824734487103}, "data": {"id": "loopAgentflow_1", "label": "Loop to Supervisor", "version": 1, "name": "loopAgentflow", "type": "Loop", "color": "#FFA07A", "hideOutput": true, "baseClasses": ["Loop"], "category": "Agent Flows", "description": "Loop back to a previous node", "inputParams": [{"label": "Loop Back To", "name": "loopBackToNode", "type": "asyncOptions", "loadMethod": "listPreviousNodes", "freeSolo": true, "id": "loopAgentflow_1-input-loopBackToNode-asyncOptions", "display": true}, {"label": "Max Loop Count", "name": "max<PERSON>oop<PERSON>ount", "type": "number", "default": 5, "id": "loopAgentflow_1-input-maxLoopCount-number", "display": true}], "inputAnchors": [], "inputs": {"loopBackToNode": "llmAgentflow_0-Supervisor", "maxLoopCount": 5}, "outputAnchors": [], "outputs": {}, "selected": false}, "type": "agent<PERSON>low", "width": 195, "height": 66, "selected": false, "dragging": false, "positionAbsolute": {"x": 566.7568359277939, "y": 90.98824734487103}}, {"id": "llmAgentflow_0", "position": {"x": -60.01488766486309, "y": 87.88377139143167}, "data": {"id": "llmAgentflow_0", "label": "Supervisor", "version": 1, "name": "llmAgentflow", "type": "LLM", "color": "#64B5F6", "baseClasses": ["LLM"], "category": "Agent Flows", "description": "Large language models to analyze user-provided inputs and generate responses", "inputParams": [{"label": "Model", "name": "llmModel", "type": "asyncOptions", "loadMethod": "listModels", "loadConfig": true, "id": "llmAgentflow_0-input-llmModel-asyncOptions", "display": true}, {"label": "Messages", "name": "llmMessages", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Role", "name": "role", "type": "options", "options": [{"label": "System", "name": "system"}, {"label": "Assistant", "name": "assistant"}, {"label": "Developer", "name": "developer"}, {"label": "User", "name": "user"}]}, {"label": "Content", "name": "content", "type": "string", "acceptVariable": true, "generateInstruction": true, "rows": 4}], "id": "llmAgentflow_0-input-llmMessages-array", "display": true}, {"label": "Enable Memory", "name": "llmEnableMemory", "type": "boolean", "description": "Enable memory for the conversation thread", "default": true, "optional": true, "id": "llmAgentflow_0-input-llmEnableMemory-boolean", "display": true}, {"label": "Memory Type", "name": "llmMemoryType", "type": "options", "options": [{"label": "All Messages", "name": "allMessages", "description": "Retrieve all messages from the conversation"}, {"label": "Window Size", "name": "windowSize", "description": "Uses a fixed window size to surface the last N messages"}, {"label": "Conversation Summary", "name": "conversation<PERSON><PERSON><PERSON><PERSON>", "description": "Summarizes the whole conversation"}, {"label": "Conversation Summary B<PERSON>er", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Summarize conversations once token limit is reached. Default to 2000"}], "optional": true, "default": "allMessages", "show": {"llmEnableMemory": true}, "id": "llmAgentflow_0-input-llmMemoryType-options", "display": true}, {"label": "Window Size", "name": "llmMemoryWindowSize", "type": "number", "default": "20", "description": "Uses a fixed window size to surface the last N messages", "show": {"llmMemoryType": "windowSize"}, "id": "llmAgentflow_0-input-llmMemoryWindowSize-number", "display": false}, {"label": "<PERSON>", "name": "llmMemoryMaxTokenLimit", "type": "number", "default": "2000", "description": "Summarize conversations once token limit is reached. Default to 2000", "show": {"llmMemoryType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "id": "llmAgentflow_0-input-llmMemoryMaxTokenLimit-number", "display": false}, {"label": "Input Message", "name": "llmUserMessage", "type": "string", "description": "Add an input message as user message at the end of the conversation", "rows": 4, "optional": true, "acceptVariable": true, "show": {"llmEnableMemory": true}, "id": "llmAgentflow_0-input-llmUserMessage-string", "display": true}, {"label": "Return Response As", "name": "llmReturnResponseAs", "type": "options", "options": [{"label": "User Message", "name": "userMessage"}, {"label": "Assistant Message", "name": "assistant<PERSON><PERSON><PERSON>"}], "default": "userMessage", "id": "llmAgentflow_0-input-llmReturnResponseAs-options", "display": true}, {"label": "JSON Structured Output", "name": "llmStructuredOutput", "description": "Instruct the LLM to give output in a JSON structured schema", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Key", "name": "key", "type": "string"}, {"label": "Type", "name": "type", "type": "options", "options": [{"label": "String", "name": "string"}, {"label": "String Array", "name": "stringArray"}, {"label": "Number", "name": "number"}, {"label": "Boolean", "name": "boolean"}, {"label": "Enum", "name": "enum"}, {"label": "JSON Array", "name": "jsonArray"}]}, {"label": "Enum Values", "name": "enum<PERSON><PERSON><PERSON>", "type": "string", "placeholder": "value1, value2, value3", "description": "Enum values. Separated by comma", "optional": true, "show": {"llmStructuredOutput[$index].type": "enum"}}, {"label": "JSON Schema", "name": "jsonSchema", "type": "code", "placeholder": "{\n    \"answer\": {\n        \"type\": \"string\",\n        \"description\": \"Value of the answer\"\n    },\n    \"reason\": {\n        \"type\": \"string\",\n        \"description\": \"Reason for the answer\"\n    },\n    \"optional\": {\n        \"type\": \"boolean\"\n    },\n    \"count\": {\n        \"type\": \"number\"\n    },\n    \"children\": {\n        \"type\": \"array\",\n        \"items\": {\n            \"type\": \"object\",\n            \"properties\": {\n                \"value\": {\n                    \"type\": \"string\",\n                    \"description\": \"Value of the children's answer\"\n                }\n            }\n        }\n    }\n}", "description": "JSON schema for the structured output", "optional": true, "show": {"llmStructuredOutput[$index].type": "jsonArray"}}, {"label": "Description", "name": "description", "type": "string", "placeholder": "Description of the key"}], "id": "llmAgentflow_0-input-llmStructuredOutput-array", "display": true}, {"label": "Update Flow State", "name": "llmUpdateState", "description": "Update runtime state during the execution of the workflow", "type": "array", "optional": true, "acceptVariable": true, "array": [{"label": "Key", "name": "key", "type": "asyncOptions", "loadMethod": "listRuntimeStateKeys", "freeSolo": true}, {"label": "Value", "name": "value", "type": "string", "acceptVariable": true, "acceptNodeOutputAsVariable": true}], "id": "llmAgentflow_0-input-llmUpdateState-array", "display": true}], "inputAnchors": [], "inputs": {"llmModel": "chatOpenAI", "llmMessages": [{"role": "system", "content": "<p>You are a supervisor tasked with managing a conversation between the following workers:</p><p>- Software Engineer</p><p>- Code Reviewer</p><p>Given the following user request, respond with the worker to act next.</p><p>Each worker will perform a task and respond with their results and status.</p><p>When finished, respond with FINISH.</p><p>Select strategically to minimize the number of steps taken.</p>"}], "llmEnableMemory": true, "llmMemoryType": "allMessages", "llmUserMessage": "<p>Given the conversation above, who should act next? Or should we FINISH? Select one of: SOFTWARE, REVIEWER</p>", "llmReturnResponseAs": "userMessage", "llmStructuredOutput": [{"key": "next", "type": "enum", "enumValues": "FINISH, SOFTWARE, REVIEWER", "jsonSchema": "", "description": "next worker to act"}, {"key": "instructions", "type": "string", "enumValues": "", "jsonSchema": "", "description": "The specific instructions of the sub-task the next worker should accomplish."}, {"key": "reasoning", "type": "string", "enumValues": "", "jsonSchema": "", "description": "The reason why next worker is tasked to do the job"}], "llmUpdateState": [{"key": "next", "value": "<p><span class=\"variable\" data-type=\"mention\" data-id=\"output.next\" data-label=\"output.next\">{{ output.next }}</span> </p>"}, {"key": "instruction", "value": "<p><span class=\"variable\" data-type=\"mention\" data-id=\"output.instructions\" data-label=\"output.instructions\">{{ output.instructions }}</span> </p>"}], "llmModelConfig": {"cache": "", "modelName": "gpt-4.1", "temperature": 0.9, "streaming": true, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "strictToolCalling": "", "stopSequence": "", "basepath": "", "proxyUrl": "", "baseOptions": "", "allowImageUploads": "", "reasoningEffort": "medium", "llmModel": "chatOpenAI"}}, "outputAnchors": [{"id": "llmAgentflow_0-output-llmAgentflow", "label": "LLM", "name": "llmAgentflow"}], "outputs": {}, "selected": false}, "type": "agent<PERSON>low", "width": 148, "height": 72, "selected": false, "positionAbsolute": {"x": -60.01488766486309, "y": 87.88377139143167}, "dragging": false}], "edges": [{"source": "startAgentflow_0", "sourceHandle": "startAgentflow_0-output-startAgentflow", "target": "llmAgentflow_0", "targetHandle": "llmAgentflow_0", "data": {"sourceColor": "#7EE787", "targetColor": "#64B5F6", "isHumanInput": false}, "type": "agent<PERSON>low", "id": "startAgentflow_0-startAgentflow_0-output-startAgentflow-llmAgentflow_0-llmAgentflow_0"}, {"source": "llmAgentflow_0", "sourceHandle": "llmAgentflow_0-output-llmAgentflow", "target": "conditionAgentflow_0", "targetHandle": "conditionAgentflow_0", "data": {"sourceColor": "#64B5F6", "targetColor": "#FFB938", "isHumanInput": false}, "type": "agent<PERSON>low", "id": "llmAgentflow_0-llmAgentflow_0-output-llmAgentflow-conditionAgentflow_0-conditionAgentflow_0"}, {"source": "conditionAgentflow_0", "sourceHandle": "conditionAgentflow_0-output-0", "target": "agentAgentflow_1", "targetHandle": "agentAgentflow_1", "data": {"sourceColor": "#FFB938", "targetColor": "#4DD0E1", "edgeLabel": "0", "isHumanInput": false}, "type": "agent<PERSON>low", "id": "conditionAgentflow_0-conditionAgentflow_0-output-0-agentAgentflow_1-agentAgentflow_1"}, {"source": "conditionAgentflow_0", "sourceHandle": "conditionAgentflow_0-output-1", "target": "agentAgentflow_2", "targetHandle": "agentAgentflow_2", "data": {"sourceColor": "#FFB938", "targetColor": "#4DD0E1", "edgeLabel": "1", "isHumanInput": false}, "type": "agent<PERSON>low", "id": "conditionAgentflow_0-conditionAgentflow_0-output-1-agentAgentflow_2-agentAgentflow_2"}, {"source": "conditionAgentflow_0", "sourceHandle": "conditionAgentflow_0-output-2", "target": "agentAgentflow_3", "targetHandle": "agentAgentflow_3", "data": {"sourceColor": "#FFB938", "targetColor": "#4DD0E1", "edgeLabel": "2", "isHumanInput": false}, "type": "agent<PERSON>low", "id": "conditionAgentflow_0-conditionAgentflow_0-output-2-agentAgentflow_3-agentAgentflow_3"}, {"source": "agentAgentflow_1", "sourceHandle": "agentAgentflow_1-output-agentAgentflow", "target": "loopAgentflow_0", "targetHandle": "loopAgentflow_0", "data": {"sourceColor": "#4DD0E1", "targetColor": "#FFA07A", "isHumanInput": false}, "type": "agent<PERSON>low", "id": "agentAgentflow_1-agentAgentflow_1-output-agentAgentflow-loopAgentflow_0-loopAgentflow_0"}, {"source": "agentAgentflow_2", "sourceHandle": "agentAgentflow_2-output-agentAgentflow", "target": "loopAgentflow_1", "targetHandle": "loopAgentflow_1", "data": {"sourceColor": "#4DD0E1", "targetColor": "#FFA07A", "isHumanInput": false}, "type": "agent<PERSON>low", "id": "agentAgentflow_2-agentAgentflow_2-output-agentAgentflow-loopAgentflow_1-loopAgentflow_1"}]}