{"name": "perplexity_ai_search", "description": "Useful when conducting research using Perplexity AI online model.", "color": "linear-gradient(rgb(155,190,84), rgb(176,69,245))", "iconSrc": "https://raw.githubusercontent.com/AsharibAli/project-images/main/perplexity-ai-icon.svg", "schema": "[{\"id\":1,\"property\":\"query\",\"description\":\"Query for research\",\"type\":\"string\",\"required\":true}]", "func": "const fetch = require('node-fetch');\nconst apiKey = '';  // Put your Perplexity API key here.\n\nconst query = $query;\n\nconst options = {\n\tmethod: 'POST',\n\theaders: {\n\t\t'Content-Type': 'application/json',\n\t\t'Authorization': `Bearer ${apiKey}`\n\t},\n\tbody: JSON.stringify({\n\t\tmodel: 'sonar',  // Model\n\t\tmessages: [\n\t\t\t{\n\t\t\t\trole: 'system',\n\t\t\t\tcontent: 'You are a market research assistant.'\n\t\t\t},\n\t\t\t{\n\t\t\t\trole: 'user',\n\t\t\t\tcontent: query\n\t\t\t}\n\t\t]\n\t})\n};\n\ntry {\n\tconsole.log(`Sending request with query: ${query}`);\n\tconst response = await fetch('https://api.perplexity.ai/chat/completions', options);\n\tconst data = await response.json();\n\n\tif (!response.ok) {\n\t\tthrow new Error(`API error: ${response.status} ${response.statusText} - ${JSON.stringify(data)}`);\n\t}\n\n\tconsole.log('API response:', data);\n\treturn JSON.stringify(data);\n} catch (error) {\n\tconsole.error('Error occurred while fetching data from Perplexity AI:', error);\n\treturn `Error occurred while fetching data from Perplexity AI: ${error.message}`;\n}"}