{"name": "print_or_export_text_document", "description": "Print or export text content to various formats. Supported `inType` values include md, html, and fountain, while supported `outType` values include pdf, epub, zip, and docx. The default `inType` is md, and the default `outType` is pdf. Provide a concise file name for the document in the `name` field. Once the print or export process is initiated, a JSON response will be returned.", "color": "linear-gradient(rgb(166,129,168), rgb(132,109,140))", "iconSrc": "https://raw.githubusercontent.com/JotterPad/.github/refs/heads/main/jotterpad.svg", "schema": "[{\"id\":0,\"property\":\"inType\",\"description\":\"Input type of the document: md, html, fountain\",\"type\":\"string\",\"required\":false},{\"id\":1,\"property\":\"outType\",\"description\":\"Output type of the document: pdf, epub, zip, docx\",\"type\":\"string\",\"required\":false},{\"id\":2,\"property\":\"input\",\"description\":\"Input content of the document.\",\"type\":\"string\",\"required\":false},{\"id\":3,\"property\":\"metadata\",\"description\":\"Metadata of document in JSON string.\",\"type\":\"string\",\"required\":false},{\"id\":4,\"property\":\"name\",\"description\":\"Short title or name of the document.\",\"type\":\"string\",\"required\":false}]", "func": "const fetch = require('node-fetch');\nconst url = 'https://jotterpad.app/publicApi/v1';\nconst token = 'API_KEY';\nconst frontmatter = `---\nbaseDocumentClass: article\ndocumentClassArg: 12pt,a4paper\ngeometry: left=15mm,right=15mm,bindingoffset=0mm,top=8mm,bottom=15mm\nnoIndent: True\n---\n\n`; // Refer to https://docs.jotterpad.app, https://jotterpad.app/app/templates or https://blog.jotterpad.app/flowise-jotterpads-print-engine/ on how to use.\n\nconst input = frontmatter + ((typeof $input !== \"undefined\") ? $input : '');\n\nconst initOptions = {\n\tmethod: 'POST',\n\theaders: {\n\t\t'Authorization': `Apikey ${token}`,\n\t\t'Content-Type': 'application/json'\n\t},\n\tbody: JSON.stringify({\n\t\tinput,\n\t\tmetadata: (typeof $metadata !== \"undefined\") ? $metadata : '',\n\t\tname: (typeof $name !== \"undefined\") ? $name : 'Untitled Document'\n\t})\n};\n\nconst sleep = (ms) => {\n\treturn new Promise((resolve) => {\n\t\tsetTimeout(resolve, ms);\n\t});\n}\n\ntry {\n\tconst inType = (typeof $inType !== \"undefined\") ? $inType : 'md';\n\tconst outType = (typeof $outType !== \"undefined\") ? $outType : 'pdf';\n\tconst initResponse = await fetch(`${url}/exports/upload/${inType}/${outType}`, initOptions);\n\tconst initJson = await initResponse.json();\n\n\tif (initJson.status === 'ok' && initJson.id) { \n\t\tconst exportId = initJson.id;\n\t\tconst getOptions = {\n\t\t\tmethod: 'POST',\n\t\t\theaders: {\n\t\t\t\t'Authorization': `Apikey ${token}`,\n\t\t\t\t'Content-Type': 'application/json'\n\t\t\t},\n\t\t\tbody: JSON.stringify({\n\t\t\t\texportId\n\t\t\t})\n\t\t};\n\n\t\tfor (let k = 0 ; k < 14 ; k++) {\n\t\t\tawait sleep(k < 6 ? 20000 : 60000);\n\t\t\tconst getResponse = await fetch(`${url}/exports/get`, getOptions);\n\t\t\tconst getJson = await getResponse.json();\n\t\t\tif (getJson.status === 'ok' && getJson.exportedFile) {\n                if (getJson.exportedFile.downloadUrl) {\n    \t\t\t\treturn JSON.stringify({\"url\": getJson.exportedFile.downloadUrl, \"input\": input});\n                }\n\t\t\t} else {\n\t\t\t\tconsole.error(getJson);\n            \treturn JSON.stringify({\"error\": `Reason: Couldn't locate document`});\n\t\t\t}\n\t\t}\n      \n\t} else if (initJson.status === 'auth_required') {\n\t\tconsole.error(initJson);\n\t\treturn JSON.stringify({\"error\": 'Reason: Invalid API Token'});\n\t} else if (initJson.status) {\n\t\tconsole.error(initJson);\n\t\treturn JSON.stringify({\"error\": `Reason: ${initJson.status}`});\n\t} else {\n\t\tconsole.error(initJson);\n    \treturn JSON.stringify({\"error\": `Reason: Unknown`});\n\t}\n} catch (error) {\n\tconsole.error(error);\n\treturn JSON.stringify({\"error\": `Reason: Unknown`});\n}"}