{"description": "Basic example of stateless (no memory) LLM Chain with a Prompt Template and LLM Model", "usecases": ["Basic"], "framework": ["Langchain"], "nodes": [{"width": 300, "height": 513, "id": "promptTemplate_0", "position": {"x": 531.9134589269008, "y": 221.7536201276406}, "type": "customNode", "data": {"id": "promptTemplate_0", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_0-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_0-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "What is a good name for a company that makes {product}?", "promptValues": "{\"product\":\"{{question}}\"}"}, "outputAnchors": [{"id": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 531.9134589269008, "y": 221.7536201276406}, "dragging": false}, {"width": 300, "height": 508, "id": "llmChain_0", "position": {"x": 907.9962733908701, "y": 252.11408353903892}, "type": "customNode", "data": {"id": "llmChain_0", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_0-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_0-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_0-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_0-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_0-input-inputModeration-Moderation"}], "inputs": {"model": "{{azureChatOpenAI_0.data.instance}}", "prompt": "{{promptTemplate_0.data.instance}}", "outputParser": "", "chainName": "", "inputModeration": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_0-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_0-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 907.9962733908701, "y": 252.11408353903892}, "dragging": false}, {"id": "azureChatOpenAI_0", "position": {"x": 175.23795705962158, "y": 101.11789404501121}, "type": "customNode", "data": {"id": "azureChatOpenAI_0", "label": "Azure ChatOpenAI", "version": 4, "name": "azureChatOpenAI", "type": "AzureChatOpenAI", "baseClasses": ["AzureChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around Azure OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["azureOpenAIApi"], "id": "azureChatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "id": "azureChatOpenAI_0-input-modelName-asyncOptions"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "azureChatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "azureChatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "azureChatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "azureChatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "azureChatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "azureChatOpenAI_0-input-timeout-number"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "azureChatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "azureChatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "azureChatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-35-turbo", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "azureChatOpenAI_0-output-azureChatOpenAI-AzureChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "azureChatOpenAI", "label": "AzureChatOpenAI", "description": "Wrapper around Azure OpenAI large language models that use the Chat endpoint", "type": "AzureChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 670, "selected": false, "positionAbsolute": {"x": 175.23795705962158, "y": 101.11789404501121}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": 900.2319450077418, "y": 59.0163203023601}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Question asked in the chat will be taken as value for {product} in the prompt.\n\nExample question:\n- socks\n- hats\n- pants"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 163, "selected": false, "positionAbsolute": {"x": 900.2319450077418, "y": 59.0163203023601}, "dragging": false}], "edges": [{"source": "promptTemplate_0", "sourceHandle": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate", "target": "llmChain_0", "targetHandle": "llmChain_0-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_0-promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate-llmChain_0-llmChain_0-input-prompt-BasePromptTemplate", "data": {"label": ""}}, {"source": "azureChatOpenAI_0", "sourceHandle": "azureChatOpenAI_0-output-azureChatOpenAI-AzureChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "azureChatOpenAI_0-azureChatOpenAI_0-output-azureChatOpenAI-AzureChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_0-llmChain_0-input-model-BaseLanguageModel"}]}