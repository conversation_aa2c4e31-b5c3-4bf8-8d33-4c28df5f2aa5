{"description": "Tool agent that can retrieve answers from multiple sources using relevant Retriever Tools", "usecases": ["Documents QnA"], "framework": ["Langchain"], "nodes": [{"width": 300, "height": 606, "id": "pinecone_0", "position": {"x": 417.52955058511066, "y": -148.13795216290424}, "type": "customNode", "data": {"id": "pinecone_0", "label": "Pinecone", "version": 3, "name": "pinecone", "type": "Pinecone", "baseClasses": ["Pinecone", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "Upsert embedded data and perform similarity or mmr search using Pinecone, a leading fully managed hosted vector database", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["pineconeApi"], "id": "pinecone_0-input-credential-credential"}, {"label": "Pinecone Index", "name": "pineconeIndex", "type": "string", "id": "pinecone_0-input-pineconeIndex-string"}, {"label": "Pinecone Namespace", "name": "pineconeNamespace", "type": "string", "placeholder": "my-first-namespace", "additionalParams": true, "optional": true, "id": "pinecone_0-input-pineconeNamespace-string"}, {"label": "Pinecone Metadata Filter", "name": "pineconeMetadataFilter", "type": "json", "optional": true, "additionalParams": true, "id": "pinecone_0-input-pineconeMetadataFilter-json"}, {"label": "Top K", "name": "topK", "description": "Number of top results to fetch. De<PERSON><PERSON> to 4", "placeholder": "4", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-topK-number"}, {"label": "Search Type", "name": "searchType", "type": "options", "default": "similarity", "options": [{"label": "Similarity", "name": "similarity"}, {"label": "<PERSON>ginal Relevance", "name": "mmr"}], "additionalParams": true, "optional": true, "id": "pinecone_0-input-searchType-options"}, {"label": "Fetch K (for MMR Search)", "name": "fetchK", "description": "Number of initial documents to fetch for MMR reranking. Default to 20. Used only when the search type is MMR", "placeholder": "20", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-fetchK-number"}, {"label": "Lambda (for MMR Search)", "name": "lambda", "description": "Number between 0 and 1 that determines the degree of diversity among the results, where 0 corresponds to maximum diversity and 1 to minimum diversity. Used only when the search type is MMR", "placeholder": "0.5", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_0-input-lambda-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "optional": true, "id": "pinecone_0-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "pinecone_0-input-embeddings-Embeddings"}, {"label": "Record Manager", "name": "recordManager", "type": "RecordManager", "description": "Keep track of the record to prevent duplication", "optional": true, "id": "pinecone_0-input-recordManager-RecordManager"}], "inputs": {"document": "", "embeddings": "{{openAIEmbeddings_0.data.instance}}", "recordManager": "", "pineconeIndex": "newindex", "pineconeNamespace": "pinecone-form10k", "pineconeMetadataFilter": "{\"source\":\"apple\"}", "topK": "", "searchType": "similarity", "fetchK": "", "lambda": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "pinecone_0-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "Pinecone Retriever", "description": "", "type": "Pinecone | VectorStoreRetriever | BaseRetriever"}, {"id": "pinecone_0-output-vectorStore-Pinecone|VectorStore", "name": "vectorStore", "label": "Pinecone Vector Store", "description": "", "type": "Pinecone | VectorStore"}], "default": "retriever"}], "outputs": {"output": "retriever"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 417.52955058511066, "y": -148.13795216290424}, "dragging": false}, {"width": 300, "height": 424, "id": "openAIEmbeddings_0", "position": {"x": 54.119166092646566, "y": -20.12821243199312}, "type": "customNode", "data": {"id": "openAIEmbeddings_0", "label": "OpenAI Embeddings", "version": 4, "name": "openAIEmbeddings", "type": "OpenAIEmbeddings", "baseClasses": ["OpenAIEmbeddings", "Embeddings"], "category": "Embeddings", "description": "OpenAI API to generate embeddings for a given text", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "openAIEmbeddings_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "text-embedding-ada-002", "id": "openAIEmbeddings_0-input-modelName-asyncOptions"}, {"label": "Strip New Lines", "name": "stripNewLines", "type": "boolean", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-stripNewLines-boolean"}, {"label": "<PERSON><PERSON> Si<PERSON>", "name": "batchSize", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-batchSize-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-basepath-string"}, {"label": "Dimensions", "name": "dimensions", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-dimensions-number"}], "inputAnchors": [], "inputs": {"modelName": "text-embedding-ada-002", "stripNewLines": "", "batchSize": "", "timeout": "", "basepath": "", "dimensions": ""}, "outputAnchors": [{"id": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "name": "openAIEmbeddings", "label": "OpenAIEmbeddings", "description": "OpenAI API to generate embeddings for a given text", "type": "OpenAIEmbeddings | Embeddings"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 54.119166092646566, "y": -20.12821243199312}, "dragging": false}, {"width": 300, "height": 606, "id": "pinecone_1", "position": {"x": 432.73419795865834, "y": 517.3146695730651}, "type": "customNode", "data": {"id": "pinecone_1", "label": "Pinecone", "version": 3, "name": "pinecone", "type": "Pinecone", "baseClasses": ["Pinecone", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "Upsert embedded data and perform similarity or mmr search using Pinecone, a leading fully managed hosted vector database", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["pineconeApi"], "id": "pinecone_1-input-credential-credential"}, {"label": "Pinecone Index", "name": "pineconeIndex", "type": "string", "id": "pinecone_1-input-pineconeIndex-string"}, {"label": "Pinecone Namespace", "name": "pineconeNamespace", "type": "string", "placeholder": "my-first-namespace", "additionalParams": true, "optional": true, "id": "pinecone_1-input-pineconeNamespace-string"}, {"label": "Pinecone Metadata Filter", "name": "pineconeMetadataFilter", "type": "json", "optional": true, "additionalParams": true, "id": "pinecone_1-input-pineconeMetadataFilter-json"}, {"label": "Top K", "name": "topK", "description": "Number of top results to fetch. De<PERSON><PERSON> to 4", "placeholder": "4", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_1-input-topK-number"}, {"label": "Search Type", "name": "searchType", "type": "options", "default": "similarity", "options": [{"label": "Similarity", "name": "similarity"}, {"label": "<PERSON>ginal Relevance", "name": "mmr"}], "additionalParams": true, "optional": true, "id": "pinecone_1-input-searchType-options"}, {"label": "Fetch K (for MMR Search)", "name": "fetchK", "description": "Number of initial documents to fetch for MMR reranking. Default to 20. Used only when the search type is MMR", "placeholder": "20", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_1-input-fetchK-number"}, {"label": "Lambda (for MMR Search)", "name": "lambda", "description": "Number between 0 and 1 that determines the degree of diversity among the results, where 0 corresponds to maximum diversity and 1 to minimum diversity. Used only when the search type is MMR", "placeholder": "0.5", "type": "number", "additionalParams": true, "optional": true, "id": "pinecone_1-input-lambda-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "optional": true, "id": "pinecone_1-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "pinecone_1-input-embeddings-Embeddings"}, {"label": "Record Manager", "name": "recordManager", "type": "RecordManager", "description": "Keep track of the record to prevent duplication", "optional": true, "id": "pinecone_1-input-recordManager-RecordManager"}], "inputs": {"document": "", "embeddings": "{{openAIEmbeddings_1.data.instance}}", "recordManager": "", "pineconeIndex": "newindex", "pineconeNamespace": "pinecone-form10k-2", "pineconeMetadataFilter": "{\"source\":\"tesla\"}", "topK": "", "searchType": "similarity", "fetchK": "", "lambda": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "pinecone_1-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "Pinecone Retriever", "description": "", "type": "Pinecone | VectorStoreRetriever | BaseRetriever"}, {"id": "pinecone_1-output-vectorStore-Pinecone|VectorStore", "name": "vectorStore", "label": "Pinecone Vector Store", "description": "", "type": "Pinecone | VectorStore"}], "default": "retriever"}], "outputs": {"output": "retriever"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 432.73419795865834, "y": 517.3146695730651}, "dragging": false}, {"width": 300, "height": 424, "id": "openAIEmbeddings_1", "position": {"x": 58.45057557109914, "y": 575.7733202609951}, "type": "customNode", "data": {"id": "openAIEmbeddings_1", "label": "OpenAI Embeddings", "version": 4, "name": "openAIEmbeddings", "type": "OpenAIEmbeddings", "baseClasses": ["OpenAIEmbeddings", "Embeddings"], "category": "Embeddings", "description": "OpenAI API to generate embeddings for a given text", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "openAIEmbeddings_1-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "text-embedding-ada-002", "id": "openAIEmbeddings_1-input-modelName-asyncOptions"}, {"label": "Strip New Lines", "name": "stripNewLines", "type": "boolean", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-stripNewLines-boolean"}, {"label": "<PERSON><PERSON> Si<PERSON>", "name": "batchSize", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-batchSize-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-basepath-string"}, {"label": "Dimensions", "name": "dimensions", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_1-input-dimensions-number"}], "inputAnchors": [], "inputs": {"modelName": "text-embedding-ada-002", "stripNewLines": "", "batchSize": "", "timeout": "", "basepath": "", "dimensions": ""}, "outputAnchors": [{"id": "openAIEmbeddings_1-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "name": "openAIEmbeddings", "label": "OpenAIEmbeddings", "description": "OpenAI API to generate embeddings for a given text", "type": "OpenAIEmbeddings | Embeddings"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 58.45057557109914, "y": 575.7733202609951}, "dragging": false}, {"width": 300, "height": 253, "id": "bufferMemory_0", "position": {"x": 805.4218592927105, "y": 1137.3074383419469}, "type": "customNode", "data": {"id": "bufferMemory_0", "label": "Buffer Memory", "version": 2, "name": "bufferMemory", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseChatMemory", "BaseMemory"], "category": "Memory", "description": "Retrieve chat messages stored in database", "inputParams": [{"label": "Session Id", "name": "sessionId", "type": "string", "description": "If not specified, a random id will be used. Learn <a target=\"_blank\" href=\"https://docs.flowiseai.com/memory#ui-and-embedded-chat\">more</a>", "default": "", "additionalParams": true, "optional": true, "id": "bufferMemory_0-input-sessionId-string"}, {"label": "Memory Key", "name": "<PERSON><PERSON><PERSON>", "type": "string", "default": "chat_history", "additionalParams": true, "id": "bufferMemory_0-input-memoryKey-string"}], "inputAnchors": [], "inputs": {"sessionId": "", "memoryKey": "chat_history"}, "outputAnchors": [{"id": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "name": "bufferMemory", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Retrieve chat messages stored in database", "type": "BufferMemory | BaseChatMemory | BaseMemory"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 805.4218592927105, "y": 1137.3074383419469}, "dragging": false}, {"width": 300, "height": 603, "id": "retrieverTool_2", "position": {"x": 798.3128281367018, "y": -151.77659673435184}, "type": "customNode", "data": {"id": "retrieverTool_2", "label": "Retriever Tool", "version": 2, "name": "retrieverTool", "type": "RetrieverTool", "baseClasses": ["RetrieverTool", "DynamicTool", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Use a retriever as allowed tool for agent", "inputParams": [{"label": "Retriever Name", "name": "name", "type": "string", "placeholder": "search_state_of_union", "id": "retrieverTool_2-input-name-string"}, {"label": "Retriever Description", "name": "description", "type": "string", "description": "When should agent uses to retrieve documents", "rows": 3, "placeholder": "Searches and returns documents regarding the state-of-the-union.", "id": "retrieverTool_2-input-description-string"}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true, "id": "retrieverTool_2-input-returnSourceDocuments-boolean"}], "inputAnchors": [{"label": "Retriever", "name": "retriever", "type": "BaseRetriever", "id": "retrieverTool_2-input-retriever-BaseRetriever"}], "inputs": {"name": "search_apple", "description": "Use this function to answer user questions about Apple Inc (APPL). It contains a SEC Form 10K filing describing the financials of Apple Inc (APPL) for the 2022 time period.", "retriever": "{{pinecone_0.data.instance}}", "returnSourceDocuments": true}, "outputAnchors": [{"id": "retrieverTool_2-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "name": "retrieverTool", "label": "RetrieverTool", "type": "RetrieverTool | DynamicTool | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 798.3128281367018, "y": -151.77659673435184}, "dragging": false}, {"width": 300, "height": 603, "id": "retrieverTool_1", "position": {"x": 805.1192462354428, "y": 479.4961512574057}, "type": "customNode", "data": {"id": "retrieverTool_1", "label": "Retriever Tool", "version": 2, "name": "retrieverTool", "type": "RetrieverTool", "baseClasses": ["RetrieverTool", "DynamicTool", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Use a retriever as allowed tool for agent", "inputParams": [{"label": "Retriever Name", "name": "name", "type": "string", "placeholder": "search_state_of_union", "id": "retrieverTool_1-input-name-string"}, {"label": "Retriever Description", "name": "description", "type": "string", "description": "When should agent uses to retrieve documents", "rows": 3, "placeholder": "Searches and returns documents regarding the state-of-the-union.", "id": "retrieverTool_1-input-description-string"}, {"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true, "id": "retrieverTool_1-input-returnSourceDocuments-boolean"}], "inputAnchors": [{"label": "Retriever", "name": "retriever", "type": "BaseRetriever", "id": "retrieverTool_1-input-retriever-BaseRetriever"}], "inputs": {"name": "search_tsla", "description": "Use this function to answer user questions about Tesla Inc (TSLA). It contains a SEC Form 10K filing describing the financials of Tesla Inc (TSLA) for the 2022 time period.", "retriever": "{{pinecone_1.data.instance}}", "returnSourceDocuments": true}, "outputAnchors": [{"id": "retrieverTool_1-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "name": "retrieverTool", "label": "RetrieverTool", "type": "RetrieverTool | DynamicTool | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 805.1192462354428, "y": 479.4961512574057}, "dragging": false}, {"id": "chatOpenAI_0", "position": {"x": 1160.0862472447252, "y": 605.506982115898}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-asyncOptions"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 670, "selected": false, "positionAbsolute": {"x": 1160.0862472447252, "y": 605.506982115898}, "dragging": false}, {"id": "toolAgent_0", "position": {"x": 1557.897498996615, "y": 415.17324915263646}, "type": "customNode", "data": {"id": "toolAgent_0", "label": "Tool Agent", "version": 1, "name": "toolAgent", "type": "AgentExecutor", "baseClasses": ["AgentExecutor", "BaseChain", "Runnable"], "category": "Agents", "description": "Agent that uses Function Calling to pick the tools and args to call", "inputParams": [{"label": "System Message", "name": "systemMessage", "type": "string", "default": "You are a helpful AI assistant.", "rows": 4, "optional": true, "additionalParams": true, "id": "toolAgent_0-input-systemMessage-string"}, {"label": "Max Iterations", "name": "maxIterations", "type": "number", "optional": true, "additionalParams": true, "id": "toolAgent_0-input-maxIterations-number"}], "inputAnchors": [{"label": "Tools", "name": "tools", "type": "Tool", "list": true, "id": "toolAgent_0-input-tools-Tool"}, {"label": "Memory", "name": "memory", "type": "BaseChatMemory", "id": "toolAgent_0-input-memory-BaseChatMemory"}, {"label": "Tool Calling Chat Model", "name": "model", "type": "BaseChatModel", "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, ChatVertexAI, GroqChat", "id": "toolAgent_0-input-model-BaseChatModel"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "toolAgent_0-input-inputModeration-Moderation"}], "inputs": {"tools": ["{{retrieverTool_1.data.instance}}", "{{retrieverTool_2.data.instance}}"], "memory": "{{bufferMemory_0.data.instance}}", "model": "{{chatOpenAI_0.data.instance}}", "systemMessage": "You are a helpful AI assistant.", "inputModeration": "", "maxIterations": ""}, "outputAnchors": [{"id": "toolAgent_0-output-toolAgent-AgentExecutor|BaseChain|Runnable", "name": "toolAgent", "label": "AgentExecutor", "description": "Agent that uses Function Calling to pick the tools and args to call", "type": "AgentExecutor | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 435, "selected": false, "positionAbsolute": {"x": 1557.897498996615, "y": 415.17324915263646}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": 412.4825307414748, "y": -350.94571995872616}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "The metadata filtering is limited to:\n\n{ source: apple }\n\nThis ensure only embeddings with specified metadata to be searched, ensuring accurate and concise data to be fed into LLM"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 183, "selected": false, "positionAbsolute": {"x": 412.4825307414748, "y": -350.94571995872616}, "dragging": false}, {"id": "stickyNote_1", "position": {"x": 97.50620416692945, "y": 418.4866537187119}, "type": "stickyNote", "data": {"id": "stickyNote_1", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_1-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Similarly, metadata filtering is limited to:\n\n{ source: tesla }\n\nto ensure only specific embeddings to be fetched"}, "outputAnchors": [{"id": "stickyNote_1-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 143, "selected": false, "positionAbsolute": {"x": 97.50620416692945, "y": 418.4866537187119}, "dragging": false}, {"id": "stickyNote_2", "position": {"x": 1548.4303201171722, "y": 297.55572308302555}, "type": "stickyNote", "data": {"id": "stickyNote_2", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_2-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Depending on user question, Tool Agent will able to decide which tool to use, OR using both tools."}, "outputAnchors": [{"id": "stickyNote_2-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 82, "selected": false, "positionAbsolute": {"x": 1548.4303201171722, "y": 297.55572308302555}, "dragging": false}], "edges": [{"source": "openAIEmbeddings_0", "sourceHandle": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "pinecone_0", "targetHandle": "pinecone_0-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_0-openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-pinecone_0-pinecone_0-input-embeddings-Embeddings"}, {"source": "openAIEmbeddings_1", "sourceHandle": "openAIEmbeddings_1-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "pinecone_1", "targetHandle": "pinecone_1-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_1-openAIEmbeddings_1-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-pinecone_1-pinecone_1-input-embeddings-Embeddings"}, {"source": "pinecone_0", "sourceHandle": "pinecone_0-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "target": "retrieverTool_2", "targetHandle": "retrieverTool_2-input-retriever-BaseRetriever", "type": "buttonedge", "id": "pinecone_0-pinecone_0-output-retriever-<PERSON>con<PERSON>|VectorStoreRetriever|BaseRetriever-retrieverTool_2-retrieverTool_2-input-retriever-BaseRetriever"}, {"source": "pinecone_1", "sourceHandle": "pinecone_1-output-retriever-Pinecone|VectorStoreRetriever|BaseRetriever", "target": "retrieverTool_1", "targetHandle": "retrieverTool_1-input-retriever-BaseRetriever", "type": "buttonedge", "id": "pinecone_1-pinecone_1-output-retriever-<PERSON>con<PERSON>|VectorStoreRetriever|BaseRetriever-retrieverTool_1-retrieverTool_1-input-retriever-BaseRetriever"}, {"source": "bufferMemory_0", "sourceHandle": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-memory-BaseChatMemory", "type": "buttonedge", "id": "bufferMemory_0-bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory-toolAgent_0-toolAgent_0-input-memory-BaseChatMemory"}, {"source": "retrieverTool_1", "sourceHandle": "retrieverTool_1-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-tools-Tool", "type": "buttonedge", "id": "retrieverTool_1-retrieverTool_1-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable-toolAgent_0-toolAgent_0-input-tools-Tool"}, {"source": "retrieverTool_2", "sourceHandle": "retrieverTool_2-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-tools-Tool", "type": "buttonedge", "id": "retrieverTool_2-retrieverTool_2-output-retrieverTool-RetrieverTool|DynamicTool|Tool|StructuredTool|Runnable-toolAgent_0-toolAgent_0-input-tools-Tool"}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "toolAgent_0", "targetHandle": "toolAgent_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-toolAgent_0-toolAgent_0-input-model-BaseChatModel"}]}