{"description": "An agent that uses ReAct (Reason + Act) logic to decide what action to take", "usecases": ["Agent"], "framework": ["Langchain"], "nodes": [{"width": 300, "height": 143, "id": "calculator_1", "position": {"x": 466.86432329033937, "y": 235.98158789908442}, "type": "customNode", "data": {"id": "calculator_1", "label": "Calculator", "version": 1, "name": "calculator", "type": "Calculator", "baseClasses": ["Calculator", "Tool", "StructuredTool", "BaseLangChain"], "category": "Tools", "description": "Perform calculations on response", "inputParams": [], "inputAnchors": [], "inputs": {}, "outputAnchors": [{"id": "calculator_1-output-calculator-Calculator|Tool|StructuredTool|BaseLangChain", "name": "calculator", "label": "Calculator", "type": "Calculator | Tool | StructuredTool | BaseLangChain"}], "outputs": {}, "selected": false}, "positionAbsolute": {"x": 466.86432329033937, "y": 235.98158789908442}, "selected": false, "dragging": false}, {"id": "chatOpenAI_0", "position": {"x": 81.2222202723384, "y": 59.395597724017364}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 670, "selected": false, "positionAbsolute": {"x": 81.2222202723384, "y": 59.395597724017364}, "dragging": false}, {"id": "bufferMemory_0", "position": {"x": 467.5487883440105, "y": 425.5853290438628}, "type": "customNode", "data": {"id": "bufferMemory_0", "label": "Buffer Memory", "version": 2, "name": "bufferMemory", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseChatMemory", "BaseMemory"], "category": "Memory", "description": "Retrieve chat messages stored in database", "inputParams": [{"label": "Session Id", "name": "sessionId", "type": "string", "description": "If not specified, a random id will be used. Learn <a target=\"_blank\" href=\"https://docs.flowiseai.com/memory#ui-and-embedded-chat\">more</a>", "default": "", "additionalParams": true, "optional": true, "id": "bufferMemory_0-input-sessionId-string"}, {"label": "Memory Key", "name": "<PERSON><PERSON><PERSON>", "type": "string", "default": "chat_history", "additionalParams": true, "id": "bufferMemory_0-input-memoryKey-string"}], "inputAnchors": [], "inputs": {"sessionId": "", "memoryKey": "chat_history"}, "outputAnchors": [{"id": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "name": "bufferMemory", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Retrieve chat messages stored in database", "type": "BufferMemory | BaseChatMemory | BaseMemory"}], "outputs": {}, "selected": false}, "width": 300, "height": 253, "selected": false, "positionAbsolute": {"x": 467.5487883440105, "y": 425.5853290438628}, "dragging": false}, {"id": "googleCustomSearch_0", "position": {"x": 468.5319676071002, "y": -72.88655734265808}, "type": "customNode", "data": {"id": "googleCustomSearch_0", "label": "Google Custom Search", "version": 1, "name": "googleCustomSearch", "type": "GoogleCustomSearchAPI", "baseClasses": ["GoogleCustomSearchAPI", "Tool", "StructuredTool", "Runnable"], "category": "Tools", "description": "Wrapper around Google Custom Search API - a real-time API to access Google search results", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["googleCustomSearchApi"], "id": "googleCustomSearch_0-input-credential-credential"}], "inputAnchors": [], "inputs": {}, "outputAnchors": [{"id": "googleCustomSearch_0-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable", "name": "googleCustomSearch", "label": "GoogleCustomSearchAPI", "description": "Wrapper around Google Custom Search API - a real-time API to access Google search results", "type": "GoogleCustomSearchAPI | Tool | StructuredTool | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 276, "selected": false, "positionAbsolute": {"x": 468.5319676071002, "y": -72.88655734265808}, "dragging": false}, {"id": "reactAgentChat_0", "position": {"x": 880.48407884172, "y": 237.79808979371387}, "type": "customNode", "data": {"id": "reactAgentChat_0", "label": "ReAct Agent for Chat Models", "version": 4, "name": "reactAgentChat", "type": "AgentExecutor", "baseClasses": ["AgentExecutor", "BaseChain", "Runnable"], "category": "Agents", "description": "Agent that uses the ReAct logic to decide what action to take, optimized to be used with Chat Models", "inputParams": [{"label": "Max Iterations", "name": "maxIterations", "type": "number", "optional": true, "additionalParams": true, "id": "reactAgentChat_0-input-maxIterations-number"}], "inputAnchors": [{"label": "Allowed Tools", "name": "tools", "type": "Tool", "list": true, "id": "reactAgentChat_0-input-tools-Tool"}, {"label": "Chat Model", "name": "model", "type": "BaseChatModel", "id": "reactAgentChat_0-input-model-BaseChatModel"}, {"label": "Memory", "name": "memory", "type": "BaseChatMemory", "id": "reactAgentChat_0-input-memory-BaseChatMemory"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "reactAgentChat_0-input-inputModeration-Moderation"}], "inputs": {"tools": ["{{googleCustomSearch_0.data.instance}}", "{{calculator_1.data.instance}}"], "model": "{{chatOpenAI_0.data.instance}}", "memory": "{{bufferMemory_0.data.instance}}", "inputModeration": "", "maxIterations": ""}, "outputAnchors": [{"id": "reactAgentChat_0-output-reactAgentChat-AgentExecutor|BaseChain|Runnable", "name": "reactAgentChat", "label": "AgentExecutor", "description": "Agent that uses the ReAct logic to decide what action to take, optimized to be used with Chat Models", "type": "AgentExecutor | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 435, "selected": false, "positionAbsolute": {"x": 880.48407884172, "y": 237.79808979371387}, "dragging": false}], "edges": [{"source": "googleCustomSearch_0", "sourceHandle": "googleCustomSearch_0-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable", "target": "reactAgentChat_0", "targetHandle": "reactAgentChat_0-input-tools-Tool", "type": "buttonedge", "id": "googleCustomSearch_0-googleCustomSearch_0-output-googleCustomSearch-GoogleCustomSearchAPI|Tool|StructuredTool|Runnable-reactAgentChat_0-reactAgentChat_0-input-tools-Tool"}, {"source": "calculator_1", "sourceHandle": "calculator_1-output-calculator-Calculator|Tool|StructuredTool|BaseLangChain", "target": "reactAgentChat_0", "targetHandle": "reactAgentChat_0-input-tools-Tool", "type": "buttonedge", "id": "calculator_1-calculator_1-output-calculator-Calculator|Tool|StructuredTool|BaseLangChain-reactAgentChat_0-reactAgentChat_0-input-tools-Tool"}, {"source": "bufferMemory_0", "sourceHandle": "bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory", "target": "reactAgentChat_0", "targetHandle": "reactAgentChat_0-input-memory-BaseChatMemory", "type": "buttonedge", "id": "bufferMemory_0-bufferMemory_0-output-bufferMemory-BufferMemory|BaseChatMemory|BaseMemory-reactAgentChat_0-reactAgentChat_0-input-memory-BaseChatMemory"}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "reactAgentChat_0", "targetHandle": "reactAgentChat_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-reactAgentChat_0-reactAgentChat_0-input-model-BaseChatModel"}]}