{"description": "Generate image using Replicate Stability text-to-image generative AI model", "framework": ["Langchain"], "usecases": ["Image Generation"], "nodes": [{"width": 300, "height": 513, "id": "promptTemplate_0", "position": {"x": 366.28009688480114, "y": 183.05394484895152}, "type": "customNode", "data": {"id": "promptTemplate_0", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_0-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_0-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "{query}", "promptValues": "{\"query\":\"{{question}}\"}"}, "outputAnchors": [{"id": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 366.28009688480114, "y": 183.05394484895152}, "dragging": false}, {"width": 300, "height": 513, "id": "promptTemplate_1", "position": {"x": 1391.1872909364881, "y": 274.0360952991433}, "type": "customNode", "data": {"id": "promptTemplate_1", "label": "Prompt Template", "version": 1, "name": "promptTemplate", "type": "PromptTemplate", "baseClasses": ["PromptTemplate", "BaseStringPromptTemplate", "BasePromptTemplate", "Runnable"], "category": "Prompts", "description": "Schema to represent a basic prompt for an LLM", "inputParams": [{"label": "Template", "name": "template", "type": "string", "rows": 4, "placeholder": "What is a good name for a company that makes {product}?", "id": "promptTemplate_1-input-template-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "promptTemplate_1-input-promptValues-json"}], "inputAnchors": [], "inputs": {"template": "Reply with nothing else but the following:\n![]({text})", "promptValues": "{\"text\":\"{{llmChain_0.data.instance}}\"}"}, "outputAnchors": [{"id": "promptTemplate_1-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "name": "promptTemplate", "label": "PromptTemplate", "type": "PromptTemplate | BaseStringPromptTemplate | BasePromptTemplate | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1391.1872909364881, "y": 274.0360952991433}, "dragging": false}, {"width": 300, "height": 577, "id": "replicate_0", "position": {"x": 700.5657822436667, "y": -192.57827891379952}, "type": "customNode", "data": {"id": "replicate_0", "label": "Replicate", "version": 2, "name": "replicate", "type": "Replicate", "baseClasses": ["Replicate", "BaseChatModel", "LLM", "BaseLLM", "BaseLanguageModel", "Runnable"], "category": "LLMs", "description": "Use Replicate to run open source models on cloud", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["replicateApi"], "id": "replicate_0-input-credential-credential"}, {"label": "Model", "name": "model", "type": "string", "placeholder": "a16z-infra/llama13b-v2-chat:df7690f1994d94e96ad9d568eac121aecf50684a0b0963b25a41cc40061269e5", "optional": true, "id": "replicate_0-input-model-string"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "description": "Adjusts randomness of outputs, greater than 1 is random and 0 is deterministic, 0.75 is a good starting value.", "default": 0.7, "optional": true, "id": "replicate_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "description": "Maximum number of tokens to generate. A word is generally 2-3 tokens", "optional": true, "additionalParams": true, "id": "replicate_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "description": "When decoding text, samples from the top p percentage of most likely tokens; lower to ignore less likely tokens", "optional": true, "additionalParams": true, "id": "replicate_0-input-topP-number"}, {"label": "Repetition Penalty", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "number", "step": 0.1, "description": "Penalty for repeated words in generated text; 1 is no penalty, values greater than 1 discourage repetition, less than 1 encourage it. (minimum: 0.01; maximum: 5)", "optional": true, "additionalParams": true, "id": "replicate_0-input-repetitionPenalty-number"}, {"label": "Additional Inputs", "name": "additionalInputs", "type": "json", "description": "Each model has different parameters, refer to the specific model accepted inputs. For example: <a target=\"_blank\" href=\"https://replicate.com/a16z-infra/llama13b-v2-chat/api#inputs\">llama13b-v2</a>", "additionalParams": true, "optional": true, "id": "replicate_0-input-additionalInputs-json"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "replicate_0-input-cache-BaseCache"}], "inputs": {"cache": "", "model": "stability-ai/sdxl:af1a68a271597604546c09c64aabcd7782c114a63539a4a8d14d1eeda5630c33", "temperature": 0.7, "maxTokens": "", "topP": "", "repetitionPenalty": "", "additionalInputs": ""}, "outputAnchors": [{"id": "replicate_0-output-replicate-Replicate|BaseChatModel|LLM|BaseLLM|BaseLanguageModel|Runnable", "name": "replicate", "label": "Replicate", "type": "Replicate | BaseChatModel | LLM | BaseLLM | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 700.5657822436667, "y": -192.57827891379952}, "dragging": false}, {"width": 300, "height": 508, "id": "llmChain_0", "position": {"x": 1036.2168666805817, "y": 252.83869526902453}, "type": "customNode", "data": {"id": "llmChain_0", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_0-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_0-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_0-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_0-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_0-input-inputModeration-Moderation"}], "inputs": {"model": "{{replicate_0.data.instance}}", "prompt": "{{promptTemplate_0.data.instance}}", "outputParser": "", "chainName": "", "inputModeration": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_0-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_0-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "outputPrediction"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1036.2168666805817, "y": 252.83869526902453}, "dragging": false}, {"width": 300, "height": 508, "id": "llmChain_1", "position": {"x": 1769.7463380379868, "y": 194.56291579865376}, "type": "customNode", "data": {"id": "llmChain_1", "label": "LLM Chain", "version": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseChain", "Runnable"], "category": "Chains", "description": "Chain to run queries against LLMs", "inputParams": [{"label": "Chain Name", "name": "chainName", "type": "string", "placeholder": "Name Your Chain", "optional": true, "id": "llmChain_1-input-chainName-string"}], "inputAnchors": [{"label": "Language Model", "name": "model", "type": "BaseLanguageModel", "id": "llmChain_1-input-model-BaseLanguageModel"}, {"label": "Prompt", "name": "prompt", "type": "BasePromptTemplate", "id": "llmChain_1-input-prompt-BasePromptTemplate"}, {"label": "Output Parser", "name": "outputParser", "type": "BaseLLMOutputParser", "optional": true, "id": "llmChain_1-input-outputParser-BaseLLMOutputParser"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "llmChain_1-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_0.data.instance}}", "prompt": "{{promptTemplate_1.data.instance}}", "outputParser": "", "chainName": "", "inputModeration": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "llmChain_1-output-llmChain-LLMChain|BaseChain|Runnable", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "LLM Chain", "type": "LLMChain | BaseChain | Runnable"}, {"id": "llmChain_1-output-outputPrediction-string|json", "name": "outputPrediction", "label": "Output Prediction", "type": "string | json"}], "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": {"output": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1769.7463380379868, "y": 194.56291579865376}, "dragging": false}, {"width": 300, "height": 670, "id": "chatOpenAI_0", "position": {"x": 1395.7716036892518, "y": -415.72370274275096}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": false, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1395.7716036892518, "y": -415.72370274275096}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": 1766.9902171902506, "y": 22.813703651766104}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Instruct LLM to response in a markdown format in order to display image in the chat window\n\nExample question:\na cat painting"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 143, "selected": false, "positionAbsolute": {"x": 1766.9902171902506, "y": 22.813703651766104}, "dragging": false}], "edges": [{"source": "promptTemplate_0", "sourceHandle": "promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate", "target": "llmChain_0", "targetHandle": "llmChain_0-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_0-promptTemplate_0-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate-llmChain_0-llmChain_0-input-prompt-BasePromptTemplate", "data": {"label": ""}}, {"source": "replicate_0", "sourceHandle": "replicate_0-output-replicate-Replicate|BaseChatModel|LLM|BaseLLM|BaseLanguageModel|Runnable", "target": "llmChain_0", "targetHandle": "llmChain_0-input-model-BaseLanguageModel", "type": "buttonedge", "id": "replicate_0-replicate_0-output-replicate-Replicate|BaseChatModel|LLM|BaseLLM|BaseLanguageModel|Runnable-llmChain_0-llmChain_0-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "promptTemplate_1", "sourceHandle": "promptTemplate_1-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable", "target": "llmChain_1", "targetHandle": "llmChain_1-input-prompt-BasePromptTemplate", "type": "buttonedge", "id": "promptTemplate_1-promptTemplate_1-output-promptTemplate-PromptTemplate|BaseStringPromptTemplate|BasePromptTemplate|Runnable-llmChain_1-llmChain_1-input-prompt-BasePromptTemplate", "data": {"label": ""}}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "llmChain_1", "targetHandle": "llmChain_1-input-model-BaseLanguageModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-llmChain_1-llmChain_1-input-model-BaseLanguageModel", "data": {"label": ""}}, {"source": "llmChain_0", "sourceHandle": "llmChain_0-output-outputPrediction-string|json", "target": "promptTemplate_1", "targetHandle": "promptTemplate_1-input-promptValues-json", "type": "buttonedge", "id": "llmChain_0-llmChain_0-output-outputPrediction-string|json-promptTemplate_1-promptTemplate_1-input-promptValues-json", "data": {"label": ""}}]}