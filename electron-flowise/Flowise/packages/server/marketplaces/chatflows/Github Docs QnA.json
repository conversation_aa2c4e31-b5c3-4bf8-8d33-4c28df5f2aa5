{"description": "Github Docs QnA using Retrieval Augmented Generation (RAG)", "usecases": ["Documents QnA"], "framework": ["Langchain"], "nodes": [{"width": 300, "height": 378, "id": "markdownTextSplitter_0", "position": {"x": 1081.1540334344143, "y": -113.73571627207801}, "type": "customNode", "data": {"id": "markdownTextSplitter_0", "label": "Markdown Text Splitter", "version": 1, "name": "markdownTextSplitter", "type": "MarkdownTextSplitter", "baseClasses": ["MarkdownTextSplitter", "RecursiveCharacterTextSplitter", "TextSplitter", "BaseDocumentTransformer"], "category": "Text Splitters", "description": "Split your content into documents based on the Markdown headers", "inputParams": [{"label": "Chunk Size", "name": "chunkSize", "type": "number", "default": 1000, "optional": true, "id": "markdownTextSplitter_0-input-chunkSize-number"}, {"label": "<PERSON><PERSON>", "name": "chunkOverlap", "type": "number", "optional": true, "id": "markdownTextSplitter_0-input-chunkOverlap-number"}], "inputAnchors": [], "inputs": {"chunkSize": "4000", "chunkOverlap": ""}, "outputAnchors": [{"id": "markdownTextSplitter_0-output-markdownTextSplitter-MarkdownTextSplitter|RecursiveCharacterTextSplitter|TextSplitter|BaseDocumentTransformer", "name": "markdownTextSplitter", "label": "MarkdownTextSplitter", "type": "MarkdownTextSplitter | RecursiveCharacterTextSplitter | TextSplitter | BaseDocumentTransformer"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1081.1540334344143, "y": -113.73571627207801}, "dragging": false}, {"width": 300, "height": 407, "id": "memoryVectorStore_0", "position": {"x": 1844.88052464165, "y": 484.60473328470243}, "type": "customNode", "data": {"id": "memoryVectorStore_0", "label": "In-Memory Vector Store", "version": 1, "name": "memoryVectorStore", "type": "Memory", "baseClasses": ["Memory", "VectorStoreRetriever", "BaseRetriever"], "category": "Vector Stores", "description": "In-memory vectorstore that stores embeddings and does an exact, linear search for the most similar embeddings.", "inputParams": [{"label": "Top K", "name": "topK", "description": "Number of top results to fetch. De<PERSON><PERSON> to 4", "placeholder": "4", "type": "number", "optional": true, "id": "memoryVectorStore_0-input-topK-number"}], "inputAnchors": [{"label": "Document", "name": "document", "type": "Document", "list": true, "id": "memoryVectorStore_0-input-document-Document"}, {"label": "Embeddings", "name": "embeddings", "type": "Embeddings", "id": "memoryVectorStore_0-input-embeddings-Embeddings"}], "inputs": {"document": ["{{github_0.data.instance}}"], "embeddings": "{{openAIEmbeddings_0.data.instance}}", "topK": ""}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "options": [{"id": "memoryVectorStore_0-output-retriever-Memory|VectorStoreRetriever|BaseRetriever", "name": "retriever", "label": "Memory Retriever", "type": "Memory | VectorStoreRetriever | BaseRetriever"}, {"id": "memoryVectorStore_0-output-vectorStore-Memory|VectorStore", "name": "vectorStore", "label": "Memory Vector Store", "type": "Memory | VectorStore"}], "default": "retriever"}], "outputs": {"output": "retriever"}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1844.88052464165, "y": 484.60473328470243}, "dragging": false}, {"width": 300, "height": 532, "id": "conversationalRetrievalQAChain_0", "position": {"x": 2262.1986022669694, "y": 229.38589782758842}, "type": "customNode", "data": {"id": "conversationalRetrievalQAChain_0", "label": "Conversational Retrieval QA Chain", "version": 3, "name": "conversationalRetrie<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "ConversationalRetrievalQAChain", "baseClasses": ["ConversationalRetrievalQAChain", "BaseChain", "Runnable"], "category": "Chains", "description": "Document QA - built on RetrievalQAChain to provide a chat history component", "inputParams": [{"label": "Return Source Documents", "name": "returnSourceDocuments", "type": "boolean", "optional": true, "id": "conversationalRetrievalQAChain_0-input-returnSourceDocuments-boolean"}, {"label": "Rephrase Prompt", "name": "rephrasePrompt", "type": "string", "description": "Using previous chat history, rephrase question into a standalone question", "warning": "Prompt must include input variables: {chat_history} and {question}", "rows": 4, "additionalParams": true, "optional": true, "default": "Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question.\n\nChat History:\n{chat_history}\nFollow Up Input: {question}\nStandalone Question:", "id": "conversationalRetrievalQ<PERSON>hain_0-input-rephrasePrompt-string"}, {"label": "Response Prompt", "name": "responsePrompt", "type": "string", "description": "Taking the rephrased question, search for answer from the provided context", "warning": "Prompt must include input variable: {context}", "rows": 4, "additionalParams": true, "optional": true, "default": "You are a helpful assistant. Using the provided context, answer the user's question to the best of your ability using the resources provided.\nIf there is nothing in the context relevant to the question at hand, just say \"Hmm, I'm not sure.\" Don't try to make up an answer.\n------------\n{context}\n------------\nREMEMBER: If there is no relevant information within the context, just say \"Hmm, I'm not sure.\" Don't try to make up an answer.", "id": "conversationalRetrievalQAChain_0-input-responsePrompt-string"}], "inputAnchors": [{"label": "Chat Model", "name": "model", "type": "BaseChatModel", "id": "conversationalRetrievalQAChain_0-input-model-BaseChatModel"}, {"label": "Vector Store Retriever", "name": "vectorStoreRetriever", "type": "BaseRetriever", "id": "conversationalRetrievalQAChain_0-input-vectorStoreRetriever-BaseRetriever"}, {"label": "Memory", "name": "memory", "type": "BaseMemory", "optional": true, "description": "If left empty, a default BufferMemory will be used", "id": "conversationalRetrievalQAChain_0-input-memory-BaseMemory"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "conversationalRetrievalQAChain_0-input-inputModeration-Moderation"}], "inputs": {"inputModeration": "", "model": "{{chatOpenAI_0.data.instance}}", "vectorStoreRetriever": "{{memoryVectorStore_0.data.instance}}", "memory": "", "returnSourceDocuments": true, "rephrasePrompt": "Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question.\n\nChat History:\n{chat_history}\nFollow Up Input: {question}\nStandalone Question:", "responsePrompt": "You are a helpful assistant. Using the provided context, answer the user's question to the best of your ability using the resources provided.\nIf there is nothing in the context relevant to the question at hand, just say \"Hmm, I'm not sure.\" Don't try to make up an answer.\n------------\n{context}\n------------\nREMEMBER: If there is no relevant information within the context, just say \"Hmm, I'm not sure.\" Don't try to make up an answer."}, "outputAnchors": [{"id": "conversationalRetrievalQAChain_0-output-conversationalRetrievalQAChain-ConversationalRetrievalQAChain|BaseChain|Runnable", "name": "conversationalRetrie<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "ConversationalRetrievalQAChain", "type": "ConversationalRetrievalQAChain | BaseChain | Runnable"}], "outputs": {}, "selected": false}, "selected": false, "dragging": false, "positionAbsolute": {"x": 2262.1986022669694, "y": 229.38589782758842}}, {"width": 300, "height": 673, "id": "github_0", "position": {"x": 1460.1858988997, "y": -137.83585695472374}, "type": "customNode", "data": {"id": "github_0", "label": "<PERSON><PERSON><PERSON>", "version": 2, "name": "github", "type": "Document", "baseClasses": ["Document"], "category": "Document Loaders", "description": "Load data from a GitHub repository", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "description": "Only needed when accessing private repo", "optional": true, "credentialNames": ["githubApi"], "id": "github_0-input-credential-credential"}, {"label": "Repo Link", "name": "repoLink", "type": "string", "placeholder": "https://github.com/FlowiseAI/Flowise", "id": "github_0-input-repoLink-string"}, {"label": "Branch", "name": "branch", "type": "string", "default": "main", "id": "github_0-input-branch-string"}, {"label": "Recursive", "name": "recursive", "type": "boolean", "optional": true, "id": "github_0-input-recursive-boolean"}, {"label": "<PERSON> Concurrency", "name": "maxConcurrency", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "github_0-input-maxConcurrency-number"}, {"label": "Ignore Paths", "name": "ignore<PERSON><PERSON>", "type": "string", "description": "An array of paths to be ignored", "placeholder": "[\"*.md\"]", "rows": 4, "optional": true, "additionalParams": true, "id": "github_0-input-ignorePath-string"}, {"label": "Max Retries", "name": "maxRetries", "description": "The maximum number of retries that can be made for a single call, with an exponential backoff between each attempt. Defaults to 2.", "type": "number", "optional": true, "additionalParams": true, "id": "github_0-input-maxRetries-number"}, {"label": "<PERSON><PERSON><PERSON>", "name": "metadata", "type": "json", "optional": true, "additionalParams": true, "id": "github_0-input-metadata-json"}], "inputAnchors": [{"label": "Text Splitter", "name": "textSplitter", "type": "TextSplitter", "optional": true, "id": "github_0-input-textSplitter-TextSplitter"}], "inputs": {"repoLink": "https://github.com/FlowiseAI/FlowiseDocs", "branch": "main", "recursive": true, "textSplitter": "{{markdownTextSplitter_0.data.instance}}", "metadata": ""}, "outputAnchors": [{"id": "github_0-output-github-Document", "name": "github", "label": "Document", "type": "Document"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1460.1858988997, "y": -137.83585695472374}, "dragging": false}, {"width": 300, "height": 670, "id": "chatOpenAI_0", "position": {"x": 1848.10147093022, "y": -213.12507406389523}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-options"}, {"label": "Temperature", "name": "temperature", "type": "number", "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"modelName": "gpt-3.5-turbo", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": true, "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "name": "chatOpenAI", "label": "ChatOpenAI", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel"}], "outputs": {}, "selected": false}, "selected": false, "positionAbsolute": {"x": 1848.10147093022, "y": -213.12507406389523}, "dragging": false}, {"width": 300, "height": 424, "id": "openAIEmbeddings_0", "position": {"x": 1114.6807349284306, "y": 482.2324008293234}, "type": "customNode", "data": {"id": "openAIEmbeddings_0", "label": "OpenAI Embeddings", "version": 4, "name": "openAIEmbeddings", "type": "OpenAIEmbeddings", "baseClasses": ["OpenAIEmbeddings", "Embeddings"], "category": "Embeddings", "description": "OpenAI API to generate embeddings for a given text", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "openAIEmbeddings_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "text-embedding-ada-002", "id": "openAIEmbeddings_0-input-modelName-asyncOptions"}, {"label": "Strip New Lines", "name": "stripNewLines", "type": "boolean", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-stripNewLines-boolean"}, {"label": "<PERSON><PERSON> Si<PERSON>", "name": "batchSize", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-batchSize-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-basepath-string"}, {"label": "Dimensions", "name": "dimensions", "type": "number", "optional": true, "additionalParams": true, "id": "openAIEmbeddings_0-input-dimensions-number"}], "inputAnchors": [], "inputs": {"modelName": "text-embedding-ada-002", "stripNewLines": "", "batchSize": "", "timeout": "", "basepath": "", "dimensions": ""}, "outputAnchors": [{"id": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "name": "openAIEmbeddings", "label": "OpenAIEmbeddings", "description": "OpenAI API to generate embeddings for a given text", "type": "OpenAIEmbeddings | Embeddings"}], "outputs": {}, "selected": false}, "selected": false, "dragging": false, "positionAbsolute": {"x": 1114.6807349284306, "y": 482.2324008293234}}, {"id": "stickyNote_0", "position": {"x": 1119.05414840041, "y": 304.34680059348875}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Recursively load files from Github repo, and split into chunks according to Markdown syntax.\n\nFor private repo, you need to connect Github credential."}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 143, "selected": false, "positionAbsolute": {"x": 1119.05414840041, "y": 304.34680059348875}, "dragging": false}, {"id": "stickyNote_1", "position": {"x": 1481.99061810943, "y": 600.8550429213293}, "type": "stickyNote", "data": {"id": "stickyNote_1", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_1-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Store the embeddings in-memory"}, "outputAnchors": [{"id": "stickyNote_1-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 42, "selected": false, "positionAbsolute": {"x": 1481.99061810943, "y": 600.8550429213293}, "dragging": false}, {"id": "stickyNote_2", "position": {"x": 2599.168985347108, "y": 244.87044713398404}, "type": "stickyNote", "data": {"id": "stickyNote_2", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_2-input-note-string"}], "inputAnchors": [], "inputs": {"note": "Conversational Retrieval QA Chain composes of 2 chains:\n\n1. A chain to rephrase user question using previous conversations\n2. A chain to provide response based on the context fetched from vector store.\n\nWhy is the need for rephrasing question?\nThis is to ensure that a follow-up question can be asked. For example:\n\n- What is the address of the Bakery shop?\n- What about the opening time?\n\nA rephrased question will be:\n- What is the opening time of the Bakery shop?\n\nThis ensure a better search to vector store, hence better output quality.\n"}, "outputAnchors": [{"id": "stickyNote_2-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 465, "selected": false, "positionAbsolute": {"x": 2599.168985347108, "y": 244.87044713398404}, "dragging": false}], "edges": [{"source": "memoryVectorStore_0", "sourceHandle": "memoryVectorStore_0-output-retriever-Memory|VectorStoreRetriever|BaseRetriever", "target": "conversationalRetrievalQAChain_0", "targetHandle": "conversationalRetrievalQAChain_0-input-vectorStoreRetriever-BaseRetriever", "type": "buttonedge", "id": "memoryVectorStore_0-memoryVectorStore_0-output-retriever-Memory|VectorStoreRetriever|BaseRetriever-conversationalRetrievalQAChain_0-conversationalRetrievalQAChain_0-input-vectorStoreRetriever-BaseRetriever", "data": {"label": ""}}, {"source": "markdownTextSplitter_0", "sourceHandle": "markdownTextSplitter_0-output-markdownTextSplitter-MarkdownTextSplitter|RecursiveCharacterTextSplitter|TextSplitter|BaseDocumentTransformer", "target": "github_0", "targetHandle": "github_0-input-textSplitter-TextSplitter", "type": "buttonedge", "id": "markdownTextSplitter_0-markdownTextSplitter_0-output-markdownTextSplitter-MarkdownTextSplitter|RecursiveCharacterTextSplitter|TextSplitter|BaseDocumentTransformer-github_0-github_0-input-textSplitter-TextSplitter", "data": {"label": ""}}, {"source": "github_0", "sourceHandle": "github_0-output-github-Document", "target": "memoryVectorStore_0", "targetHandle": "memoryVectorStore_0-input-document-Document", "type": "buttonedge", "id": "github_0-github_0-output-github-Document-memoryVectorStore_0-memoryVectorStore_0-input-document-Document", "data": {"label": ""}}, {"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel", "target": "conversationalRetrievalQAChain_0", "targetHandle": "conversationalRetrievalQAChain_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel-conversationalRetrievalQAChain_0-conversationalRetrievalQAChain_0-input-model-BaseChatModel", "data": {"label": ""}}, {"source": "openAIEmbeddings_0", "sourceHandle": "openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings", "target": "memoryVectorStore_0", "targetHandle": "memoryVectorStore_0-input-embeddings-Embeddings", "type": "buttonedge", "id": "openAIEmbeddings_0-openAIEmbeddings_0-output-openAIEmbeddings-OpenAIEmbeddings|Embeddings-memoryVectorStore_0-memoryVectorStore_0-input-embeddings-Embeddings", "data": {"label": ""}}]}