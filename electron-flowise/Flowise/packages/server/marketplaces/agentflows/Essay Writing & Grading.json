{"description": "One agent that writes essay, and another agent that grades the essay. Then loop back to first agent until the condition is met", "framework": ["Langchain"], "usecases": ["Reflective Agent"], "nodes": [{"id": "seqStart_0", "position": {"x": 214.34509608830842, "y": 210}, "type": "customNode", "data": {"id": "seqStart_0", "label": "Start", "version": 2, "name": "seqStart", "type": "Start", "baseClasses": ["Start"], "category": "Sequential Agents", "description": "Starting point of the conversation", "inputParams": [], "inputAnchors": [{"label": "Chat Model", "name": "model", "type": "BaseChatModel", "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, ChatVertexAI, GroqChat", "id": "seqStart_0-input-model-BaseChatModel"}, {"label": "Agent Memory", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "BaseCheckpointSaver", "description": "Save the state of the agent", "optional": true, "id": "seqStart_0-input-agentMemory-BaseCheckpointSaver"}, {"label": "State", "name": "state", "type": "State", "description": "State is an object that is updated by nodes in the graph, passing from one node to another. By default, state contains \"messages\" that got updated with each message sent and received.", "optional": true, "id": "seqStart_0-input-state-State"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "seqStart_0-input-inputModeration-Moderation"}], "inputs": {"model": "{{chatOpenAI_0.data.instance}}", "agentMemory": "{{agentMemory_0.data.instance}}", "state": "", "inputModeration": ""}, "outputAnchors": [{"id": "seqStart_0-output-seqStart-Start", "name": "seqStart", "label": "Start", "description": "Starting point of the conversation", "type": "Start"}], "outputs": {}, "selected": false}, "width": 300, "height": 382, "selected": false, "positionAbsolute": {"x": 214.34509608830842, "y": 210}, "dragging": false}, {"id": "seqCondition_0", "position": {"x": 965.214404199721, "y": 200.76150141731824}, "type": "customNode", "data": {"id": "seqCondition_0", "label": "Condition", "version": 2, "name": "seqCondition", "type": "Condition", "baseClasses": ["Condition"], "category": "Sequential Agents", "description": "Conditional function to determine which route to take next", "inputParams": [{"label": "Condition Name", "name": "conditionName", "type": "string", "optional": true, "placeholder": "If X, then Y", "id": "seqCondition_0-input-conditionName-string"}, {"label": "Condition", "name": "condition", "type": "conditionFunction", "tabIdentifier": "selectedConditionFunctionTab", "tabs": [{"label": "Condition (Table)", "name": "conditionUI", "type": "datagrid", "description": "If a condition is met, the node connected to the respective output will be executed", "optional": true, "datagrid": [{"field": "variable", "headerName": "Variable", "type": "freeSolo", "editable": true, "loadMethod": ["getPreviousMessages", "loadStateKeys"], "valueOptions": [{"label": "Total Messages (number)", "value": "$flow.state.messages.length"}, {"label": "First Message Content (string)", "value": "$flow.state.messages[0].content"}, {"label": "Last Message Content (string)", "value": "$flow.state.messages[-1].content"}, {"label": "Global variable (string)", "value": "$vars.<variable-name>"}], "flex": 0.5, "minWidth": 200}, {"field": "operation", "headerName": "Operation", "type": "singleSelect", "valueOptions": ["Contains", "Not Contains", "Start With", "End With", "Is", "Is Not", "Is Empty", "Is Not Empty", "Greater Than", "Less Than", "Equal To", "Not Equal To", "Greater Than or Equal To", "Less Than or Equal To"], "editable": true, "flex": 0.4, "minWidth": 150}, {"field": "value", "headerName": "Value", "flex": 1, "editable": true}, {"field": "output", "headerName": "Output Name", "editable": true, "flex": 0.3, "minWidth": 150}]}, {"label": "Condition (Code)", "name": "conditionFunction", "type": "code", "description": "Function to evaluate the condition", "hint": {"label": "How to use", "value": "\n1. Must return a string value at the end of function. For example:\n    ```js\n    if (\"X\" === \"X\") {\n        return \"Agent\"; // connect to next agent node\n    } else {\n        return \"End\"; // connect to end node\n    }\n    ```\n\n2. In most cases, you would probably get the last message to do some comparison. You can get all current messages from the state: `$flow.state.messages`:\n    ```json\n    [\n        {\n            \"content\": \"Hello! How can I assist you today?\",\n            \"name\": \"\",\n            \"additional_kwargs\": {},\n            \"response_metadata\": {},\n            \"tool_calls\": [],\n            \"invalid_tool_calls\": [],\n            \"usage_metadata\": {}\n        }\n    ]\n    ```\n\n    For example, to get the last message content:\n    ```js\n    const messages = $flow.state.messages;\n    const lastMessage = messages[messages.length - 1];\n\n    // Proceed to do something with the last message content\n    ```\n\n3. You can get default flow config, including the current \"state\":\n    - `$flow.sessionId`\n    - `$flow.chatId`\n    - `$flow.chatflowId`\n    - `$flow.input`\n    - `$flow.state`\n\n4. You can get custom variables: `$vars.<variable-name>`\n\n"}, "hideCodeExecute": true, "codeExample": "const state = $flow.state;\n                \nconst messages = state.messages;\n\nconst lastMessage = messages[messages.length - 1];\n\n/* Check if the last message has content */\nif (lastMessage.content) {\n    return \"Agent\";\n}\n\nreturn \"End\";", "optional": true}], "id": "seqCondition_0-input-condition-conditionFunction"}], "inputAnchors": [{"label": "Start | Agent | LLM | Tool Node", "name": "sequentialNode", "type": "Start | Agent | LLMNode | ToolNode", "list": true, "id": "seqCondition_0-input-sequentialNode-Start | Agent | LLMNode | ToolNode"}], "inputs": {"conditionName": "If loop > 3 times", "sequentialNode": ["{{seqLLMNode_0.data.instance}}"], "condition": "", "selectedConditionFunctionTab_seqCondition_0": "conditionUI", "conditionUI": "[{\"variable\":\"$flow.state.messages.length\",\"operation\":\"Less Than or Equal To\",\"value\":\"6\",\"output\":\"Grading\",\"actions\":\"\",\"id\":0}]"}, "outputAnchors": [{"name": "output", "label": "Output", "type": "options", "description": "", "options": [{"id": "seqCondition_0-output-end-Condition", "name": "end", "label": "End", "type": "Condition", "isAnchor": true}, {"id": "seqCondition_0-output-grading-Condition", "name": "grading", "label": "Grading", "type": "Condition", "isAnchor": true}], "default": "next"}], "outputs": {"output": "next"}, "selected": false}, "width": 300, "height": 474, "selected": false, "positionAbsolute": {"x": 965.214404199721, "y": 200.76150141731824}, "dragging": false}, {"id": "seqLLMNode_0", "position": {"x": 603.8430617050778, "y": 202.97850351851244}, "type": "customNode", "data": {"id": "seqLLMNode_0", "label": "LLM Node", "version": 2, "name": "seqLLMNode", "type": "LLMNode", "baseClasses": ["LLMNode"], "category": "Sequential Agents", "description": "Run Chat Model and return the output", "inputParams": [{"label": "Name", "name": "llmNodeName", "type": "string", "placeholder": "LLM", "id": "seqLLMNode_0-input-llmNodeName-string"}, {"label": "System Prompt", "name": "systemMessagePrompt", "type": "string", "rows": 4, "optional": true, "additionalParams": true, "id": "seqLLMNode_0-input-systemMessagePrompt-string"}, {"label": "Human Prompt", "name": "humanMessagePrompt", "type": "string", "description": "This prompt will be added at the end of the messages as human message", "rows": 4, "optional": true, "additionalParams": true, "id": "seqLLMNode_0-input-humanMessagePrompt-string"}, {"label": "Format Prompt Values", "name": "promptValues", "description": "Assign values to the prompt variables. You can also use $flow.state.<variable-name> to get the state value", "type": "json", "optional": true, "acceptVariable": true, "list": true, "additionalParams": true, "id": "seqLLMNode_0-input-promptValues-json"}, {"label": "JSON Structured Output", "name": "llmStructuredOutput", "type": "datagrid", "description": "Instruct the LLM to give output in a JSON structured schema", "datagrid": [{"field": "key", "headerName": "Key", "editable": true}, {"field": "type", "headerName": "Type", "type": "singleSelect", "valueOptions": ["String", "String Array", "Number", "Boolean", "Enum"], "editable": true}, {"field": "enum<PERSON><PERSON><PERSON>", "headerName": "Enum Values", "editable": true}, {"field": "description", "headerName": "Description", "flex": 1, "editable": true}], "optional": true, "additionalParams": true, "id": "seqLLMNode_0-input-llmStructuredOutput-datagrid"}, {"label": "Update State", "name": "updateStateMemory", "type": "tabs", "tabIdentifier": "selectedUpdateStateMemoryTab", "default": "updateStateMemoryUI", "additionalParams": true, "tabs": [{"label": "Update State (Table)", "name": "updateStateMemoryUI", "type": "datagrid", "hint": {"label": "How to use", "value": "\n1. Key and value pair to be updated. For example: if you have the following State:\n    | Key       | Operation     | Default Value     |\n    |-----------|---------------|-------------------|\n    | user      | Replace       |                   |\n\n    You can update the \"user\" value with the following:\n    | Key       | Value     |\n    |-----------|-----------|\n    | user      | john doe  |\n\n2. If you want to use the agent's output as the value to update state, it is available as available as `$flow.output` with the following structure:\n    ```json\n    {\n        \"content\": 'Hello! How can I assist you today?',\n        \"name\": \"\",\n        \"additional_kwargs\": {},\n        \"response_metadata\": {},\n        \"tool_calls\": [],\n        \"invalid_tool_calls\": [],\n        \"usage_metadata\": {}\n    }\n    ```\n\n    For example, if the output `content` is the value you want to update the state with, you can do the following:\n    | Key       | Value                     |\n    |-----------|---------------------------|\n    | user      | `$flow.output.content`  |\n\n3. You can get default flow config, including the current \"state\":\n    - `$flow.sessionId`\n    - `$flow.chatId`\n    - `$flow.chatflowId`\n    - `$flow.input`\n    - `$flow.state`\n\n4. You can get custom variables: `$vars.<variable-name>`\n\n"}, "description": "This is only applicable when you have a custom State at the START node. After agent execution, you might want to update the State values", "datagrid": [{"field": "key", "headerName": "Key", "type": "asyncSingleSelect", "loadMethod": "loadStateKeys", "flex": 0.5, "editable": true}, {"field": "value", "headerName": "Value", "type": "freeSolo", "valueOptions": [{"label": "LLM Node Output (string)", "value": "$flow.output.content"}, {"label": "LLM JSON Output Key (string)", "value": "$flow.output.<replace-with-key>"}, {"label": "Global variable (string)", "value": "$vars.<variable-name>"}, {"label": "Input Question (string)", "value": "$flow.input"}, {"label": "Session Id (string)", "value": "$flow.sessionId"}, {"label": "<PERSON><PERSON> (string)", "value": "$flow.chatId"}, {"label": "Chatflow Id (string)", "value": "$flow.chatflowId"}], "editable": true, "flex": 1}], "optional": true, "additionalParams": true}, {"label": "Update State (Code)", "name": "updateStateMemoryCode", "type": "code", "hint": {"label": "How to use", "value": "\n1. Return the key value JSON object. For example: if you have the following State:\n    ```json\n    {\n        \"user\": null\n    }\n    ```\n\n    You can update the \"user\" value by returning the following:\n    ```js\n    return {\n        \"user\": \"john doe\"\n    }\n    ```\n\n2. If you want to use the LLM Node's output as the value to update state, it is available as `$flow.output` with the following structure:\n    ```json\n    {\n        \"content\": 'Hello! How can I assist you today?',\n        \"name\": \"\",\n        \"additional_kwargs\": {},\n        \"response_metadata\": {},\n        \"tool_calls\": [],\n        \"invalid_tool_calls\": [],\n        \"usage_metadata\": {}\n    }\n    ```\n\n    For example, if the output `content` is the value you want to update the state with, you can return the following:\n    ```js\n    return {\n        \"user\": $flow.output.content\n    }\n    ```\n\n3. You can also get default flow config, including the current \"state\":\n    - `$flow.sessionId`\n    - `$flow.chatId`\n    - `$flow.chatflowId`\n    - `$flow.input`\n    - `$flow.state`\n\n4. You can get custom variables: `$vars.<variable-name>`\n\n"}, "description": "This is only applicable when you have a custom State at the START node. After agent execution, you might want to update the State values. Must return an object representing the state", "hideCodeExecute": true, "codeExample": "const result = $flow.output;\n\n/* Suppose we have a custom State schema like this:\n* {\n    aggregate: {\n        value: (x, y) => x.concat(y),\n        default: () => []\n    }\n  }\n*/\n\nreturn {\n  aggregate: [result.content]\n};", "optional": true, "additionalParams": true}], "id": "seqLLMNode_0-input-updateStateMemory-tabs"}], "inputAnchors": [{"label": "Start | Agent | Condition | LLM | Tool Node", "name": "sequentialNode", "type": "Start | Agent | Condition | LLMNode | ToolNode", "list": true, "id": "seqLLMNode_0-input-sequentialNode-Start | Agent | Condition | LLMNode | ToolNode"}, {"label": "Chat Model", "name": "model", "type": "BaseChatModel", "optional": true, "description": "Overwrite model to be used for this node", "id": "seqLLMNode_0-input-model-BaseChatModel"}], "inputs": {"llmNodeName": "Writer", "systemMessagePrompt": "You are an essay assistant tasked with writing excellent 3-paragraph essays.\nGenerate the best essay possible for the user's request.  \nIf the user provides critique, respond with a revised version of your previous attempts.", "humanMessagePrompt": "", "sequentialNode": ["{{seqStart_0.data.instance}}", "{{seqStart_0.data.instance}}"], "model": "", "promptValues": "", "llmStructuredOutput": "", "updateStateMemory": "updateStateMemoryUI"}, "outputAnchors": [{"id": "seqLLMNode_0-output-seqLLMNode-LLMNode", "name": "seqLLMNode", "label": "LLMNode", "description": "Run Chat Model and return the output", "type": "LLMNode"}], "outputs": {}, "selected": false}, "width": 300, "height": 450, "selected": false, "positionAbsolute": {"x": 603.8430617050778, "y": 202.97850351851244}, "dragging": false}, {"id": "seqLLMNode_1", "position": {"x": 1353.990019049842, "y": 463.97823544265555}, "type": "customNode", "data": {"id": "seqLLMNode_1", "label": "LLM Node", "version": 2, "name": "seqLLMNode", "type": "LLMNode", "baseClasses": ["LLMNode"], "category": "Sequential Agents", "description": "Run Chat Model and return the output", "inputParams": [{"label": "Name", "name": "llmNodeName", "type": "string", "placeholder": "LLM", "id": "seqLLMNode_1-input-llmNodeName-string"}, {"label": "System Prompt", "name": "systemMessagePrompt", "type": "string", "rows": 4, "optional": true, "additionalParams": true, "id": "seqLLMNode_1-input-systemMessagePrompt-string"}, {"label": "Human Prompt", "name": "humanMessagePrompt", "type": "string", "description": "This prompt will be added at the end of the messages as human message", "rows": 4, "optional": true, "additionalParams": true, "id": "seqLLMNode_1-input-humanMessagePrompt-string"}, {"label": "Format Prompt Values", "name": "promptValues", "description": "Assign values to the prompt variables. You can also use $flow.state.<variable-name> to get the state value", "type": "json", "optional": true, "acceptVariable": true, "list": true, "additionalParams": true, "id": "seqLLMNode_1-input-promptValues-json"}, {"label": "JSON Structured Output", "name": "llmStructuredOutput", "type": "datagrid", "description": "Instruct the LLM to give output in a JSON structured schema", "datagrid": [{"field": "key", "headerName": "Key", "editable": true}, {"field": "type", "headerName": "Type", "type": "singleSelect", "valueOptions": ["String", "String Array", "Number", "Boolean", "Enum"], "editable": true}, {"field": "enum<PERSON><PERSON><PERSON>", "headerName": "Enum Values", "editable": true}, {"field": "description", "headerName": "Description", "flex": 1, "editable": true}], "optional": true, "additionalParams": true, "id": "seqLLMNode_1-input-llmStructuredOutput-datagrid"}, {"label": "Update State", "name": "updateStateMemory", "type": "tabs", "tabIdentifier": "selectedUpdateStateMemoryTab", "default": "updateStateMemoryUI", "additionalParams": true, "tabs": [{"label": "Update State (Table)", "name": "updateStateMemoryUI", "type": "datagrid", "hint": {"label": "How to use", "value": "\n1. Key and value pair to be updated. For example: if you have the following State:\n    | Key       | Operation     | Default Value     |\n    |-----------|---------------|-------------------|\n    | user      | Replace       |                   |\n\n    You can update the \"user\" value with the following:\n    | Key       | Value     |\n    |-----------|-----------|\n    | user      | john doe  |\n\n2. If you want to use the agent's output as the value to update state, it is available as available as `$flow.output` with the following structure:\n    ```json\n    {\n        \"content\": 'Hello! How can I assist you today?',\n        \"name\": \"\",\n        \"additional_kwargs\": {},\n        \"response_metadata\": {},\n        \"tool_calls\": [],\n        \"invalid_tool_calls\": [],\n        \"usage_metadata\": {}\n    }\n    ```\n\n    For example, if the output `content` is the value you want to update the state with, you can do the following:\n    | Key       | Value                     |\n    |-----------|---------------------------|\n    | user      | `$flow.output.content`  |\n\n3. You can get default flow config, including the current \"state\":\n    - `$flow.sessionId`\n    - `$flow.chatId`\n    - `$flow.chatflowId`\n    - `$flow.input`\n    - `$flow.state`\n\n4. You can get custom variables: `$vars.<variable-name>`\n\n"}, "description": "This is only applicable when you have a custom State at the START node. After agent execution, you might want to update the State values", "datagrid": [{"field": "key", "headerName": "Key", "type": "asyncSingleSelect", "loadMethod": "loadStateKeys", "flex": 0.5, "editable": true}, {"field": "value", "headerName": "Value", "type": "freeSolo", "valueOptions": [{"label": "LLM Node Output (string)", "value": "$flow.output.content"}, {"label": "LLM JSON Output Key (string)", "value": "$flow.output.<replace-with-key>"}, {"label": "Global variable (string)", "value": "$vars.<variable-name>"}, {"label": "Input Question (string)", "value": "$flow.input"}, {"label": "Session Id (string)", "value": "$flow.sessionId"}, {"label": "<PERSON><PERSON> (string)", "value": "$flow.chatId"}, {"label": "Chatflow Id (string)", "value": "$flow.chatflowId"}], "editable": true, "flex": 1}], "optional": true, "additionalParams": true}, {"label": "Update State (Code)", "name": "updateStateMemoryCode", "type": "code", "hint": {"label": "How to use", "value": "\n1. Return the key value JSON object. For example: if you have the following State:\n    ```json\n    {\n        \"user\": null\n    }\n    ```\n\n    You can update the \"user\" value by returning the following:\n    ```js\n    return {\n        \"user\": \"john doe\"\n    }\n    ```\n\n2. If you want to use the LLM Node's output as the value to update state, it is available as `$flow.output` with the following structure:\n    ```json\n    {\n        \"content\": 'Hello! How can I assist you today?',\n        \"name\": \"\",\n        \"additional_kwargs\": {},\n        \"response_metadata\": {},\n        \"tool_calls\": [],\n        \"invalid_tool_calls\": [],\n        \"usage_metadata\": {}\n    }\n    ```\n\n    For example, if the output `content` is the value you want to update the state with, you can return the following:\n    ```js\n    return {\n        \"user\": $flow.output.content\n    }\n    ```\n\n3. You can also get default flow config, including the current \"state\":\n    - `$flow.sessionId`\n    - `$flow.chatId`\n    - `$flow.chatflowId`\n    - `$flow.input`\n    - `$flow.state`\n\n4. You can get custom variables: `$vars.<variable-name>`\n\n"}, "description": "This is only applicable when you have a custom State at the START node. After agent execution, you might want to update the State values. Must return an object representing the state", "hideCodeExecute": true, "codeExample": "const result = $flow.output;\n\n/* Suppose we have a custom State schema like this:\n* {\n    aggregate: {\n        value: (x, y) => x.concat(y),\n        default: () => []\n    }\n  }\n*/\n\nreturn {\n  aggregate: [result.content]\n};", "optional": true, "additionalParams": true}], "id": "seqLLMNode_1-input-updateStateMemory-tabs"}], "inputAnchors": [{"label": "Start | Agent | Condition | LLM | Tool Node", "name": "sequentialNode", "type": "Start | Agent | Condition | LLMNode | ToolNode", "list": true, "id": "seqLLMNode_1-input-sequentialNode-Start | Agent | Condition | LLMNode | ToolNode"}, {"label": "Chat Model", "name": "model", "type": "BaseChatModel", "optional": true, "description": "Overwrite model to be used for this node", "id": "seqLLMNode_1-input-model-BaseChatModel"}], "inputs": {"llmNodeName": "Teacher", "systemMessagePrompt": "You are a teacher grading an essay submission.\nGenerate critique and recommendations for the user's submission.\nProvide detailed recommendations, including requests for length, depth, style, etc.", "humanMessagePrompt": "", "sequentialNode": ["{{seqCondition_0.data.instance}}", "{{seqCondition_0.data.instance}}"], "model": "", "promptValues": "", "llmStructuredOutput": "", "updateStateMemory": "updateStateMemoryUI"}, "outputAnchors": [{"id": "seqLLMNode_1-output-seqLLMNode-LLMNode", "name": "seqLLMNode", "label": "LLMNode", "description": "Run Chat Model and return the output", "type": "LLMNode"}], "outputs": {}, "selected": false}, "width": 300, "height": 450, "selected": false, "positionAbsolute": {"x": 1353.990019049842, "y": 463.97823544265555}, "dragging": false}, {"id": "seqEnd_0", "position": {"x": 1346.5387656051114, "y": 280.5735770603069}, "type": "customNode", "data": {"id": "seqEnd_0", "label": "End", "version": 2, "name": "seqEnd", "type": "End", "baseClasses": ["End"], "category": "Sequential Agents", "description": "End conversation", "inputParams": [], "inputAnchors": [{"label": "Agent | Condition | LLM | Tool Node", "name": "sequentialNode", "type": "Agent | Condition | LLMNode | ToolNode", "id": "seqEnd_0-input-sequentialNode-Agent | Condition | LLMNode | ToolNode"}], "inputs": {"sequentialNode": "{{seqCondition_0.data.instance}}"}, "outputAnchors": [], "outputs": {}, "selected": false}, "width": 300, "height": 143, "selected": false, "positionAbsolute": {"x": 1346.5387656051114, "y": 280.5735770603069}, "dragging": false}, {"id": "seqLoop_0", "position": {"x": 1730.9768550888484, "y": 654.3960003607233}, "type": "customNode", "data": {"id": "seqLoop_0", "label": "Loop", "version": 2, "name": "seqL<PERSON>", "type": "Loop", "baseClasses": ["Loop"], "category": "Sequential Agents", "description": "Loop back to the specific sequential node", "inputParams": [{"label": "Loop To", "name": "loopToName", "description": "Name of the agent/llm to loop back to", "type": "string", "placeholder": "Agent", "id": "seqLoop_0-input-loopToName-string"}], "inputAnchors": [{"label": "Agent | Condition | LLM | Tool Node", "name": "sequentialNode", "type": "Agent | Condition | LLMNode | ToolNode", "list": true, "id": "seqLoop_0-input-sequentialNode-Agent | Condition | LLMNode | ToolNode"}], "inputs": {"sequentialNode": ["{{seqLLMNode_1.data.instance}}"], "loopToName": "Writer"}, "outputAnchors": [], "outputs": {}, "selected": false}, "width": 300, "height": 241, "selected": false, "dragging": false, "positionAbsolute": {"x": 1730.9768550888484, "y": 654.3960003607233}}, {"id": "agentMemory_0", "position": {"x": -156.5886590045086, "y": 200.76150141731824}, "type": "customNode", "data": {"id": "agentMemory_0", "label": "Agent Memory", "version": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON><PERSON>", "baseClasses": ["<PERSON><PERSON><PERSON><PERSON>", "BaseCheckpointSaver"], "category": "Memory", "description": "Memory for agent<PERSON> to remember the state of the conversation", "inputParams": [{"label": "Database", "name": "databaseType", "type": "options", "options": [{"label": "SQLite", "name": "sqlite"}], "default": "sqlite", "id": "agentMemory_0-input-databaseType-options"}, {"label": "Database File Path", "name": "databaseFilePath", "type": "string", "placeholder": "C:\\Users\\<USER>\\.flowise\\database.sqlite", "description": "If SQLite is selected, provide the path to the SQLite database file. Leave empty to use default application database", "additionalParams": true, "optional": true, "id": "agentMemory_0-input-databaseFilePath-string"}, {"label": "Additional Connection Configuration", "name": "additionalConfig", "type": "json", "additionalParams": true, "optional": true, "id": "agentMemory_0-input-additionalConfig-json"}], "inputAnchors": [], "inputs": {"databaseType": "sqlite", "databaseFilePath": "", "additionalConfig": ""}, "outputAnchors": [{"id": "agentMemory_0-output-agentMemory-AgentMemory|BaseCheckpointSaver", "name": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Memory for agent<PERSON> to remember the state of the conversation", "type": "AgentMemory | BaseCheckpointSaver"}], "outputs": {}, "selected": false}, "width": 300, "height": 327, "selected": false, "positionAbsolute": {"x": -156.5886590045086, "y": 200.76150141731824}, "dragging": false}, {"id": "chatOpenAI_0", "position": {"x": -520.1770036003459, "y": 112.08141736955315}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-asyncOptions"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-3.5-turbo", "temperature": 0.9, "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 669, "selected": false, "positionAbsolute": {"x": -520.1770036003459, "y": 112.08141736955315}, "dragging": false}, {"id": "stickyNote_0", "position": {"x": 217.57933661470304, "y": 82.43460481421448}, "type": "stickyNote", "data": {"id": "stickyNote_0", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_0-input-note-string"}], "inputAnchors": [], "inputs": {"note": "You can start with a question like:\n\nWrite me an essay about sky"}, "outputAnchors": [{"id": "stickyNote_0-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 82, "selected": false, "dragging": false, "positionAbsolute": {"x": 217.57933661470304, "y": 82.43460481421448}}, {"id": "stickyNote_1", "position": {"x": 969.0193366147029, "y": 112.99460481421448}, "type": "stickyNote", "data": {"id": "stickyNote_1", "label": "<PERSON><PERSON>", "version": 2, "name": "stickyNote", "type": "StickyNote", "baseClasses": ["StickyNote"], "tags": ["Utilities"], "category": "Utilities", "description": "Add a sticky note", "inputParams": [{"label": "", "name": "note", "type": "string", "rows": 1, "placeholder": "Type something here", "optional": true, "id": "stickyNote_1-input-note-string"}], "inputAnchors": [], "inputs": {"note": "This will check if the writer -> teacher loop has ran for more than 3 times, if yes, end it."}, "outputAnchors": [{"id": "stickyNote_1-output-stickyNote-StickyNote", "name": "stickyNote", "label": "StickyNote", "description": "Add a sticky note", "type": "StickyNote"}], "outputs": {}, "selected": false}, "width": 300, "height": 62, "selected": false, "dragging": false, "positionAbsolute": {"x": 969.0193366147029, "y": 112.99460481421448}}], "edges": [{"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "seqStart_0", "targetHandle": "seqStart_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-seqStart_0-seqStart_0-input-model-BaseChatModel"}, {"source": "agentMemory_0", "sourceHandle": "agentMemory_0-output-agentMemory-AgentMemory|BaseCheckpointSaver", "target": "seqStart_0", "targetHandle": "seqStart_0-input-agentMemory-BaseCheckpointSaver", "type": "buttonedge", "id": "agentMemory_0-agentMemory_0-output-agentMemory-AgentMemory|BaseCheckpointSaver-seqStart_0-seqStart_0-input-agentMemory-BaseCheckpointSaver"}, {"source": "seqLLMNode_0", "sourceHandle": "seqLLMNode_0-output-seqLLMNode-LLMNode", "target": "seqCondition_0", "targetHandle": "seqCondition_0-input-sequentialNode-Start | Agent | LLMNode | ToolNode", "type": "buttonedge", "id": "seqLLMNode_0-seqLLMNode_0-output-seqLLMNode-LLMNode-seqCondition_0-seqCondition_0-input-sequentialNode-Start | Agent | LLMNode | ToolNode"}, {"source": "seqCondition_0", "sourceHandle": "seqCondition_0-output-end-Condition", "target": "seqEnd_0", "targetHandle": "seqEnd_0-input-sequentialNode-Agent | Condition | LLMNode | ToolNode", "type": "buttonedge", "id": "seqCondition_0-seqCondition_0-output-end-Condition-seqEnd_0-seqEnd_0-input-sequentialNode-Agent | Condition | LLMNode | ToolNode"}, {"source": "seqCondition_0", "sourceHandle": "seqCondition_0-output-grading-Condition", "target": "seqLLMNode_1", "targetHandle": "seqLLMNode_1-input-sequentialNode-Start | Agent | Condition | LLMNode | ToolNode", "type": "buttonedge", "id": "seqCondition_0-seqCondition_0-output-grading-Condition-seqLLMNode_1-seqLLMNode_1-input-sequentialNode-Start | Agent | Condition | LLMNode | ToolNode"}, {"source": "seqStart_0", "sourceHandle": "seqStart_0-output-seqStart-Start", "target": "seqLLMNode_0", "targetHandle": "seqLLMNode_0-input-sequentialNode-Start | Agent | Condition | LLMNode | ToolNode", "type": "buttonedge", "id": "seqStart_0-seqStart_0-output-seqStart-Start-seqLLMNode_0-seqLLMNode_0-input-sequentialNode-Start | Agent | Condition | LLMNode | ToolNode"}, {"source": "seqLLMNode_1", "sourceHandle": "seqLLMNode_1-output-seqLLMNode-LLMNode", "target": "seqLoop_0", "targetHandle": "seqLoop_0-input-sequentialNode-Agent | Condition | LLMNode | ToolNode", "type": "buttonedge", "id": "seqLLMNode_1-seqLLMNode_1-output-seqLLMNode-LLMNode-seqLoop_0-seqLoop_0-input-sequentialNode-Agent | Condition | LLMNode | ToolNode"}]}