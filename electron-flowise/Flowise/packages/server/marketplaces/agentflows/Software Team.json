{"description": "Software engineering team working together to build a feature, solve a problem, or complete a task.", "framework": ["Langchain"], "usecases": ["Engineering", "Hierarchical Agent Teams"], "nodes": [{"id": "supervisor_0", "position": {"x": 577, "y": 156}, "type": "customNode", "data": {"id": "supervisor_0", "label": "Supervisor", "version": 1, "name": "supervisor", "type": "Supervisor", "baseClasses": ["Supervisor"], "category": "Multi Agents", "inputParams": [{"label": "Supervisor Name", "name": "<PERSON><PERSON><PERSON>", "type": "string", "placeholder": "Supervisor", "default": "Supervisor", "id": "supervisor_0-input-supervisorName-string"}, {"label": "Supervisor Prompt", "name": "supervisor<PERSON><PERSON><PERSON>", "type": "string", "description": "Prompt must contains {team_members}", "rows": 4, "default": "You are a supervisor tasked with managing a conversation between the following workers: {team_members}.\nGiven the following user request, respond with the worker to act next.\nEach worker will perform a task and respond with their results and status.\nWhen finished, respond with FINISH.\nSelect strategically to minimize the number of steps taken.", "additionalParams": true, "id": "supervisor_0-input-supervisorPrompt-string"}, {"label": "Recursion Limit", "name": "recursionLimit", "type": "number", "description": "Maximum number of times a call can recurse. If not provided, defaults to 100.", "default": 100, "additionalParams": true, "id": "supervisor_0-input-recursionLimit-number"}], "inputAnchors": [{"label": "Tool Calling Chat Model", "name": "model", "type": "BaseChatModel", "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, GroqChat. Best result with GPT-4 model", "id": "supervisor_0-input-model-BaseChatModel"}, {"label": "Input Moderation", "description": "Detect text that could generate harmful output and prevent it from being sent to the language model", "name": "inputModeration", "type": "Moderation", "optional": true, "list": true, "id": "supervisor_0-input-inputModeration-Moderation"}], "inputs": {"supervisorName": "Supervisor", "supervisorPrompt": "You are a supervisor tasked with managing a conversation between the following workers: {team_members}.\nGiven the following user request, respond with the worker to act next.\nEach worker will perform a task and respond with their results and status.\nWhen finished, respond with FINISH.\nSelect strategically to minimize the number of steps taken.", "model": "{{chatOpenAI_0.data.instance}}", "recursionLimit": 100, "inputModeration": ""}, "outputAnchors": [{"id": "supervisor_0-output-supervisor-Supervisor", "name": "supervisor", "label": "Supervisor", "description": "", "type": "Supervisor"}], "outputs": {}, "selected": false}, "width": 300, "height": 431, "positionAbsolute": {"x": 577, "y": 156}, "selected": false}, {"id": "worker_0", "position": {"x": 969.3717362716295, "y": 77.52271438462338}, "type": "customNode", "data": {"id": "worker_0", "label": "Worker", "version": 1, "name": "worker", "type": "Worker", "baseClasses": ["Worker"], "category": "Multi Agents", "inputParams": [{"label": "Worker Name", "name": "worker<PERSON>ame", "type": "string", "placeholder": "Worker", "id": "worker_0-input-workerName-string"}, {"label": "Worker Prompt", "name": "workerPrompt", "type": "string", "rows": 4, "default": "You are a research assistant who can search for up-to-date info using search engine.", "id": "worker_0-input-workerPrompt-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "worker_0-input-promptValues-json"}, {"label": "Max Iterations", "name": "maxIterations", "type": "number", "optional": true, "id": "worker_0-input-maxIterations-number"}], "inputAnchors": [{"label": "Tools", "name": "tools", "type": "Tool", "list": true, "optional": true, "id": "worker_0-input-tools-Tool"}, {"label": "Supervisor", "name": "supervisor", "type": "Supervisor", "id": "worker_0-input-supervisor-Supervisor"}, {"label": "Tool Calling Chat Model", "name": "model", "type": "BaseChatModel", "optional": true, "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, ChatVertexAI, GroqChat. If not specified, supervisor's model will be used", "id": "worker_0-input-model-BaseChatModel"}], "inputs": {"workerName": "Senior Software Engineer", "workerPrompt": "As a Senior Software Engineer at {company}, you are a pivotal part of our innovative development team. Your expertise and leadership drive the creation of robust, scalable software solutions that meet the needs of our diverse clientele. By applying best practices in software development, you ensure that our products are reliable, efficient, and maintainable.\n\nYour goal is to lead the development of high-quality software solutions.\n\nUtilize your deep technical knowledge and experience to architect, design, and implement software systems that address complex problems. Collaborate closely with other engineers, reviewers to ensure that the solutions you develop align with business objectives and user needs.\n\nDesign and implement new feature for the given task, ensuring it integrates seamlessly with existing systems and meets performance requirements. Use your understanding of {technology} to build this feature. Make sure to adhere to our coding standards and follow best practices.\n\nThe output should be a fully functional, well-documented feature that enhances our product's capabilities. Include detailed comments in the code. Pass the code to Quality Assurance Engineer for review if neccessary. Once ther review is good enough, produce a finalized version of the code.", "tools": "", "supervisor": "{{supervisor_0.data.instance}}", "model": "", "promptValues": "{\"company\":\"Flowise Inc\",\"technology\":\"React, NodeJS, ExpressJS, Tailwindcss\"}", "maxIterations": ""}, "outputAnchors": [{"id": "worker_0-output-worker-Worker", "name": "worker", "label": "Worker", "description": "", "type": "Worker"}], "outputs": {}, "selected": false}, "width": 300, "height": 808, "selected": false, "positionAbsolute": {"x": 969.3717362716295, "y": 77.52271438462338}, "dragging": false}, {"id": "worker_1", "position": {"x": 1369.3717362716295, "y": 77.52271438462338}, "type": "customNode", "data": {"id": "worker_1", "label": "Worker", "version": 1, "name": "worker", "type": "Worker", "baseClasses": ["Worker"], "category": "Multi Agents", "inputParams": [{"label": "Worker Name", "name": "worker<PERSON>ame", "type": "string", "placeholder": "Worker", "id": "worker_1-input-workerName-string"}, {"label": "Worker Prompt", "name": "workerPrompt", "type": "string", "rows": 4, "default": "You are a research assistant who can search for up-to-date info using search engine.", "id": "worker_1-input-workerPrompt-string"}, {"label": "Format Prompt Values", "name": "promptValues", "type": "json", "optional": true, "acceptVariable": true, "list": true, "id": "worker_1-input-promptValues-json"}, {"label": "Max Iterations", "name": "maxIterations", "type": "number", "optional": true, "id": "worker_1-input-maxIterations-number"}], "inputAnchors": [{"label": "Tools", "name": "tools", "type": "Tool", "list": true, "optional": true, "id": "worker_1-input-tools-Tool"}, {"label": "Supervisor", "name": "supervisor", "type": "Supervisor", "id": "worker_1-input-supervisor-Supervisor"}, {"label": "Tool Calling Chat Model", "name": "model", "type": "BaseChatModel", "optional": true, "description": "Only compatible with models that are capable of function calling: ChatOpenAI, ChatMistral, ChatAnthropic, ChatGoogleGenerativeAI, ChatVertexAI, GroqChat. If not specified, supervisor's model will be used", "id": "worker_1-input-model-BaseChatModel"}], "inputs": {"workerName": "Code Reviewer", "workerPrompt": "As a Quality Assurance Engineer at {company}, you are an integral part of our development team, ensuring that our software products are of the highest quality. Your meticulous attention to detail and expertise in testing methodologies are crucial in identifying defects and ensuring that our code meets the highest standards.\n\nYour goal is to ensure the delivery of high-quality software through thorough code review and testing.\n\nReview the codebase for the new feature designed and implemented by the Senior Software Engineer. Your expertise goes beyond mere code inspection; you are adept at ensuring that developments not only function as intended but also adhere to the team's coding standards, enhance maintainability, and seamlessly integrate with existing systems. \n\nWith a deep appreciation for collaborative development, you provide constructive feedback, guiding contributors towards best practices and fostering a culture of continuous improvement. Your meticulous approach to reviewing code, coupled with your ability to foresee potential issues and recommend proactive solutions, ensures the delivery of high-quality software that is robust, scalable, and aligned with the team's strategic goals.\n\nAlways pass back the review and feedback to Senior Software Engineer.", "tools": "", "supervisor": "{{supervisor_0.data.instance}}", "model": "", "promptValues": "{\"company\":\"Flowise Inc\"}", "maxIterations": ""}, "outputAnchors": [{"id": "worker_1-output-worker-Worker", "name": "worker", "label": "Worker", "description": "", "type": "Worker"}], "outputs": {}, "selected": false}, "width": 300, "height": 808, "selected": false, "positionAbsolute": {"x": 1369.3717362716295, "y": 77.52271438462338}, "dragging": false}, {"id": "chatOpenAI_0", "position": {"x": 201.1230948105134, "y": 70.78573663723421}, "type": "customNode", "data": {"id": "chatOpenAI_0", "label": "ChatOpenAI", "version": 6, "name": "chatOpenAI", "type": "ChatOpenAI", "baseClasses": ["ChatOpenAI", "BaseChatModel", "BaseLanguageModel", "Runnable"], "category": "Chat Models", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "inputParams": [{"label": "Connect Credential", "name": "credential", "type": "credential", "credentialNames": ["openAIApi"], "id": "chatOpenAI_0-input-credential-credential"}, {"label": "Model Name", "name": "modelName", "type": "asyncOptions", "loadMethod": "listModels", "default": "gpt-3.5-turbo", "id": "chatOpenAI_0-input-modelName-asyncOptions"}, {"label": "Temperature", "name": "temperature", "type": "number", "step": 0.1, "default": 0.9, "optional": true, "id": "chatOpenAI_0-input-temperature-number"}, {"label": "<PERSON>", "name": "maxTokens", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-maxTokens-number"}, {"label": "Top Probability", "name": "topP", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-topP-number"}, {"label": "Frequency Penalty", "name": "frequencyPenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-frequencyPenalty-number"}, {"label": "Presence Penalty", "name": "presencePenalty", "type": "number", "step": 0.1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-presencePenalty-number"}, {"label": "Timeout", "name": "timeout", "type": "number", "step": 1, "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-timeout-number"}, {"label": "BasePath", "name": "basepath", "type": "string", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-basepath-string"}, {"label": "BaseOptions", "name": "baseOptions", "type": "json", "optional": true, "additionalParams": true, "id": "chatOpenAI_0-input-baseOptions-json"}, {"label": "Allow Image Uploads", "name": "allowImageUploads", "type": "boolean", "description": "Automatically uses gpt-4-vision-preview when image is being uploaded from chat. Only works with LLMChain, Conversation Chain, ReAct Agent, and Conversational Agent", "default": false, "optional": true, "id": "chatOpenAI_0-input-allowImageUploads-boolean"}, {"label": "Image Resolution", "description": "This parameter controls the resolution in which the model views the image.", "name": "imageResolution", "type": "options", "options": [{"label": "Low", "name": "low"}, {"label": "High", "name": "high"}, {"label": "Auto", "name": "auto"}], "default": "low", "optional": false, "additionalParams": true, "id": "chatOpenAI_0-input-imageResolution-options"}], "inputAnchors": [{"label": "<PERSON><PERSON>", "name": "cache", "type": "BaseCache", "optional": true, "id": "chatOpenAI_0-input-cache-BaseCache"}], "inputs": {"cache": "", "modelName": "gpt-4o", "temperature": "0", "maxTokens": "", "topP": "", "frequencyPenalty": "", "presencePenalty": "", "timeout": "", "basepath": "", "baseOptions": "", "allowImageUploads": "", "imageResolution": "low"}, "outputAnchors": [{"id": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "name": "chatOpenAI", "label": "ChatOpenAI", "description": "Wrapper around OpenAI large language models that use the Chat endpoint", "type": "ChatOpenAI | BaseChatModel | BaseLanguageModel | Runnable"}], "outputs": {}, "selected": false}, "width": 300, "height": 669, "selected": false, "positionAbsolute": {"x": 201.1230948105134, "y": 70.78573663723421}, "dragging": false}], "edges": [{"source": "chatOpenAI_0", "sourceHandle": "chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable", "target": "supervisor_0", "targetHandle": "supervisor_0-input-model-BaseChatModel", "type": "buttonedge", "id": "chatOpenAI_0-chatOpenAI_0-output-chatOpenAI-ChatOpenAI|BaseChatModel|BaseLanguageModel|Runnable-supervisor_0-supervisor_0-input-model-BaseChatModel"}, {"source": "supervisor_0", "sourceHandle": "supervisor_0-output-supervisor-Supervisor", "target": "worker_1", "targetHandle": "worker_1-input-supervisor-Supervisor", "type": "buttonedge", "id": "supervisor_0-supervisor_0-output-supervisor-Supervisor-worker_1-worker_1-input-supervisor-Supervisor"}, {"source": "supervisor_0", "sourceHandle": "supervisor_0-output-supervisor-Supervisor", "target": "worker_0", "targetHandle": "worker_0-input-supervisor-Supervisor", "type": "buttonedge", "id": "supervisor_0-supervisor_0-output-supervisor-Supervisor-worker_0-worker_0-input-supervisor-Supervisor"}]}