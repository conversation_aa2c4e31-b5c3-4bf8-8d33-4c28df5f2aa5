{"name": "flowise-components", "version": "3.0.4", "description": "Flowiseai Components", "main": "dist/src/index", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc && gulp", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "clean": "<PERSON><PERSON><PERSON> dist", "nuke": "rimraf dist node_modules .turbo"}, "keywords": [], "homepage": "https://flowiseai.com", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"@apidevtools/json-schema-ref-parser": "^11.7.0", "@arizeai/openinference-instrumentation-langchain": "^2.0.0", "@aws-sdk/client-bedrock-runtime": "3.422.0", "@aws-sdk/client-dynamodb": "^3.360.0", "@aws-sdk/client-s3": "^3.427.0", "@aws-sdk/client-secrets-manager": "^3.699.0", "@datastax/astra-db-ts": "1.5.0", "@dqbd/tiktoken": "^1.0.21", "@e2b/code-interpreter": "^0.0.5", "@elastic/elasticsearch": "^8.9.0", "@flowiseai/nodevm": "^3.9.25", "@getzep/zep-cloud": "~1.0.7", "@getzep/zep-js": "^0.9.0", "@gomomento/sdk": "^1.51.1", "@gomomento/sdk-core": "^1.51.1", "@google-ai/generativelanguage": "^2.5.0", "@google-cloud/storage": "^7.15.2", "@google/generative-ai": "^0.24.0", "@huggingface/inference": "^2.6.1", "@langchain/anthropic": "0.3.14", "@langchain/aws": "^0.1.11", "@langchain/baidu-qianfan": "^0.1.0", "@langchain/cohere": "^0.0.7", "@langchain/community": "^0.3.29", "@langchain/core": "0.3.61", "@langchain/exa": "^0.0.5", "@langchain/google-genai": "0.2.3", "@langchain/google-vertexai": "^0.2.10", "@langchain/groq": "0.1.2", "@langchain/langgraph": "^0.0.22", "@langchain/mistralai": "^0.2.0", "@langchain/mongodb": "^0.0.1", "@langchain/ollama": "0.2.0", "@langchain/openai": "0.5.6", "@langchain/pinecone": "^0.1.3", "@langchain/qdrant": "^0.0.5", "@langchain/weaviate": "^0.0.1", "@langchain/xai": "^0.0.1", "@mem0/community": "^0.0.1", "@mendable/firecrawl-js": "^1.18.2", "@mistralai/mistralai": "0.1.3", "@modelcontextprotocol/sdk": "^1.10.1", "@modelcontextprotocol/server-brave-search": "^0.6.2", "@modelcontextprotocol/server-github": "^2025.1.23", "@modelcontextprotocol/server-postgres": "^0.6.2", "@modelcontextprotocol/server-sequential-thinking": "^0.6.2", "@modelcontextprotocol/server-slack": "^2025.1.17", "@notionhq/client": "^2.2.8", "@opensearch-project/opensearch": "^1.2.0", "@pinecone-database/pinecone": "4.0.0", "@qdrant/js-client-rest": "^1.9.0", "@stripe/agent-toolkit": "^0.1.20", "@supabase/supabase-js": "^2.29.0", "@types/js-yaml": "^4.0.5", "@types/jsdom": "^21.1.1", "@upstash/redis": "1.22.1", "@upstash/vector": "1.1.5", "@zilliz/milvus2-sdk-node": "^2.2.24", "apify-client": "^2.7.1", "assemblyai": "^4.2.2", "axios": "1.7.9", "cheerio": "^1.0.0-rc.12", "chromadb": "^1.10.0", "cohere-ai": "^7.7.5", "composio-core": "^0.4.7", "couchbase": "4.4.1", "crypto-js": "^4.1.1", "css-what": "^6.1.0", "d3-dsv": "2", "dotenv": "^16.0.0", "epub2": "^3.0.2", "exa-js": "^1.0.12", "express": "^4.17.3", "faiss-node": "^0.5.1", "fast-json-patch": "^3.1.1", "form-data": "^4.0.0", "google-auth-library": "^9.4.0", "graphql": "^16.6.0", "html-to-text": "^9.0.5", "ioredis": "^5.3.2", "jsdom": "^22.1.0", "jsonpointer": "^5.0.1", "jsonrepair": "^3.11.1", "langchain": "^0.3.5", "langfuse": "3.3.4", "langfuse-langchain": "^3.3.4", "langsmith": "0.1.6", "langwatch": "^0.1.1", "linkifyjs": "^4.1.1", "llamaindex": "^0.3.13", "lodash": "^4.17.21", "lunary": "^0.7.12", "mammoth": "^1.5.1", "meilisearch": "^0.41.0", "moment": "^2.29.3", "mongodb": "6.3.0", "mysql2": "^3.11.3", "neo4j-driver": "^5.26.0", "node-fetch": "^2.6.11", "node-html-markdown": "^1.3.0", "notion-to-md": "^3.1.1", "object-hash": "^3.0.0", "officeparser": "5.1.1", "ollama": "^0.5.11", "openai": "^4.96.0", "papaparse": "^5.4.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^3.7.107", "pg": "^8.11.2", "playwright": "^1.35.0", "puppeteer": "^20.7.1", "pyodide": ">=0.21.0-alpha.2", "redis": "^4.6.7", "remove-markdown": "^0.6.2", "replicate": "^0.31.1", "sanitize-filename": "^1.6.3", "srt-parser-2": "^1.2.3", "supergateway": "3.0.1", "typeorm": "^0.3.6", "weaviate-ts-client": "^1.1.0", "winston": "^3.9.0", "ws": "^8.18.0", "xlsx": "0.18.5", "zod": "3.22.4", "zod-to-json-schema": "^3.21.4"}, "devDependencies": {"@swc/core": "^1.3.99", "@types/crypto-js": "^4.1.1", "@types/gulp": "4.0.9", "@types/lodash": "^4.14.202", "@types/node-fetch": "2.6.2", "@types/object-hash": "^3.0.2", "@types/papaparse": "^5.3.15", "@types/pg": "^8.10.2", "@types/ws": "^8.5.3", "babel-register": "^6.26.0", "gulp": "^4.0.2", "rimraf": "^5.0.5", "tsc-watch": "^6.0.4", "tslib": "^2.6.2", "typescript": "^5.4.5"}}