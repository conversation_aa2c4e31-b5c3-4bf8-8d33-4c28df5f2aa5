{"name": "souls-flowise-monorepo", "version": "1.0.0", "private": true, "workspaces": [], "scripts": {"build": "electron-vite build", "electron": "electron-vite build && electron electron-flowise/out/main/index.js", "dev": "electron-vite build && electron-vite dev", "format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md}": "eslint --fix"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^1.0.1", "@electron-toolkit/tsconfig": "^1.0.1", "@rushstack/eslint-patch": "^1.7.1", "@types/node": "^24.0.10", "@typescript-eslint/typescript-estree": "^7.13.1", "electron": "^37.2.0", "electron-vite": "^2.0.0", "kill-port": "^2.0.1", "typescript": "5.8.3", "prettier": "^3.2.4", "vite": "4.2.0", "@vitejs/plugin-react": "^4.2.0", "dotenv": "^16.0.0", "react-router-dom": "~6.3.0", "react-dom": "^17.0.2", "react": "^17.0.2", "react-router": "~6.3.0", "@types/react": "^17.0.0"}, "dependencies": {"@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "electron-log": "^5.4.1"}, "engines": {"node": ">=18.15.0 <19.0.0 || ^20"}, "eslintIgnore": ["**/electron-flowise", "**/node_modules", "**/package-lock.json"], "prettier": {"printWidth": 140, "singleQuote": true, "jsxSingleQuote": true, "trailingComma": "none", "tabWidth": 4, "semi": false, "endOfLine": "auto"}, "main": "./electron-flowise/out/main/index.js"}