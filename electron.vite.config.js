import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import dotenv from 'dotenv'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'

export default defineConfig(async ({ mode }) => {
    dotenv.config()

    return {
        main: {
            plugins: [externalizeDepsPlugin()],
            build: {
                outDir: 'electron-flowise/out/main',
                emptyOutDir: true,
            }
        },
        preload: {
            plugins: [externalizeDepsPlugin()],
            build: {
                outDir: 'electron-flowise/out/preload',
                emptyOutDir: true,
            }
        },
        renderer: {
            plugins: [react()],
            resolve: {
                alias: {
                    '@renderer': resolve('src/renderer'),
                }
            },
            build: {
                sourcemap: true,
                outDir: 'electron-flowise/out/renderer'
            }
        }
    }
})
